package com.lj.auth.service.impl;

import com.lj.auth.entity.SpTransactionRecords;
import com.lj.auth.mapper.SpTransactionRecordsMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * ChainServiceImpl 测试类
 * 用于测试修复后的 saveTransactionInfo 方法
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional // 测试后回滚，不影响数据库
public class ChainServiceImplTest {

    @Resource
    private ChainServiceImpl chainService;

    @Resource
    private SpTransactionRecordsMapper spTransactionRecordsMapper;

    /**
     * 测试 saveTransactionInfo 方法的基本功能
     * 注意：这个测试需要真实的区块链交易哈希才能运行
     */
    @Test
    public void testSaveTransactionInfo() {
        // 这里需要一个真实的交易哈希来测试
        // 由于测试环境可能没有真实的区块链连接，这个测试主要用于验证代码结构
        String testTransactionHash = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
        
        log.info("开始测试 saveTransactionInfo 方法");
        
        try {
            // 注意：这个测试在没有真实区块链连接的情况下会失败
            // 但可以验证方法调用是否正常
            chainService.queryTxInfo(testTransactionHash);
            log.info("测试完成");
        } catch (Exception e) {
            log.info("预期的异常（因为没有真实的区块链连接）: {}", e.getMessage());
        }
    }

    /**
     * 测试数据库映射器是否正常工作
     */
    @Test
    public void testSpTransactionRecordsMapper() {
        log.info("测试 SpTransactionRecordsMapper");
        
        // 创建测试数据
        SpTransactionRecords testRecord = new SpTransactionRecords();
        testRecord.setTxHash("test_hash_" + System.currentTimeMillis());
        testRecord.setFromAddress("0x1234567890123456789012345678901234567890");
        testRecord.setToAddress("0x0987654321098765432109876543210987654321");
        testRecord.setChainId(1L);
        testRecord.setStatus(1);
        testRecord.setTxType(1);
        testRecord.setSymbol("SP");
        
        try {
            // 插入测试数据
            int result = spTransactionRecordsMapper.insert(testRecord);
            log.info("插入结果: {}, 记录ID: {}", result, testRecord.getId());
            
            // 验证插入是否成功
            assert result > 0 : "插入失败";
            assert testRecord.getId() != null : "ID 应该被自动生成";
            
            log.info("SpTransactionRecordsMapper 测试通过");
        } catch (Exception e) {
            log.error("SpTransactionRecordsMapper 测试失败", e);
            throw e;
        }
    }
}

sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期  当前设置为7天
  timeout: 604800
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false


spring:
  redis:
    database: 6
    host: **************
    port: 6379
    password: yt123
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  cloud:
    nacos:
      discovery:
        server-addr: **************
        namespace: 42ca4dce-77bb-4f62-8531-7c522cc6e3be
        port: ${server.port}
  datasource:
    url: ********************************************************************************************************************************************
    username: yzcm
    password: Yzcm123.com
    driver-class-name: com.mysql.cj.jdbc.Driver
  rabbitmq:
    host: **************
    port: 5672
    username: yzcm
    password: Yzcm123.com
    virtual-host: /lj-server
    listener:
      simple:
        acknowledge-mode: manual
      direct:
        acknowledge-mode: manual


mybatis-plus:
  # 配置mapper映射文件
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    # 正常开启日志
#        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #关闭日志打印
        log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    #设置查询结果为null,同样映射该查询字段给map
        call-setters-on-nulls: true


logging:
  level:
    root:
      info



SMSOpen: false  #短信是否开启

imagepath: /etc/nginx/html/static/upload/wallet/upimages #251服务器文件上传路径
readImagepath: https://wallet.ylzh.pro/upimages/     #251服务器读取图片路径
payCallbackNotifyAddress: https://wallet.ylzh.pro/lj-auth/   #支付回调地址


#云链智能链   测试链地址
ylznlCurl: http://*************:8550
ylChainId: 240508



#链浏览器
ylBesuChain:
  url: *********:9901



ylBesuChainContract:
  manmagerContractAddress: "******************************************"  #注册管理合约地址
  resolverContractAddress: "******************************************"  #解析合约地址
  registryContractAddress: "******************************************" #注册表合约地址



forest:
  # 关闭 forest 请求日志打印
  log-enabled: true


## 福穗灵工平台
fusui:
  gateWay: https://pre-api.fulufusui.com
  appId: "803071703229977"
  secret: l391MVV1xq8tOYTjVQIShG9P8FY7UMEXiEYMwr7n8xM=
  taxId:  1656230439994007553

idCardCheckAPPId: 2e984a3f32e249dc9e6925fbfb1dafa5 #阿里云实名信息验证APPId

ipfs:
  url: http://**************:8088/ipfs/
  imagePrefix: http://**************:8088/ipfs/
ipfsMultiAddr: /ip4/**************/tcp/5002
jsonPath: /nft-json


##接口参数加密标识
filterFlag: false

##DID签到系统
did-check-url: http://127.0.0.1:9618/did-check-in/


## 参数签名秘钥
paramSecretKey: lj_server_wish_2099


## 接口签名标识
paramSignFlag: true



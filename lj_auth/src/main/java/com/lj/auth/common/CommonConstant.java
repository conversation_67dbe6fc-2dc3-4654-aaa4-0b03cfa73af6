package com.lj.auth.common;

import java.awt.*;

public class CommonConstant {
    /**
     * 业务异常返回码
     */
    public static final Integer EXCEPTION_CODE = 500;
    /**
     * token失效
     */
    public static final Integer TOKEN_FAIL = 401;
    /**
     * 验证码失效
     */
    public static final Integer INVALID_VERIFICATION_CODE = 402;

    /**
     * 十六进制前缀
     **/
    public static final String HEX_PRE_FIX_OX = "0x";

    /**
     * 操作成功返回码
     */
    public static final Integer SUCCESS = 200;

    /**
     * 操作失败返回码
     */
    public static final Integer ERROR = 500;

    /**
     * 经销商登录是需要传的operateUuid
     */
    public static final String OPERATE_UUID = "operateUuid";

    /**
     * YL UUID
     */
    public static final String YL_UUID = "YL_UUID";

    /**
     * 短信服务商
     */
    public static final String SMS_SERVICE_PROVIDER = "sms_service_provider";

    /**
     * 云链前缀
     */
    public static final String YL_UUID_PRE = "YL";

    /**
     * 默认头像
     **/
    public static final String DEFAULT_PORTRAIT = "default_portrait";

    /**
     *
     * 游客图像
     */
    public static final String TOURIST_AVATAR = "touristAvatar";

    /**
     *
     * 注销图像
     */
    public static final String UNREGISTER_IMAGE = "unregisterImage";

    /**
     * did标识前缀
     */
    public static final String DID_SYMBOL_PREFIX = "did:ctid:bsn:";

    /**
     * 短信类型 验证码
     */
    public static final Integer SMS_TYPE_VERIFICATION_CODE = 0;

    /**
     * 用户状态 正常
     **/
    public static final Integer ACCOUNT_STATE_NORMAL = 1;

    /**
     * 用户实名状态 认证成功
     **/
    public static final Integer ACCOUNT_REALNAME_STATUS_CERTIFIED_SUCCESS = 1;

    /**
     * 应用标识
     */
    public static final String APPLICATION_SYMBOL = "application_symbol";

    // --------------------------------------------------------------------------
    // ---------------------BSN域名调用相关-----------------------------------------------------
    // --------------------------------------------------------------------------
    /**
     * 域名接口url
     */
    // public static String URL = "http://8.148.20.174:8777/";
    // 百度云
    public static String URL = "http://172.16.16.14:8777/";
    // public static String URL = "http://127.0.0.1:8777/";
    /**
     * 查询域名年服务费
     */
    public static String DOMAIN_FEE = "domainFee";
    /**
     * 查询业务费
     */
    public static String TRADE_FEE = "tradeFee";
    /**
     * 查询域名状态
     */
    public static String DOMAIN_AVAILABLE = "domainAvailable";

    /**
     * 域名注册
     */
    public static String SERVE_REGISTER = "serveRegister";
    /**
     * 域名注销
     */
    public static String SERVE_DEREGISTER = "serveDeregister";
    /**
     * 域名所有者设置
     */
    public static String SET_OWNER = "setOwner";
    /**
     * 域名管理者设置
     */
    public static String SET_ADMIN = "setAdmin";
    /**
     * 查询域名服务结果
     */
    public static String SERVE_SEARCH = "serveSearch";
    /**
     * 查询域名详情
     */
    public static String DOMAIN_SEARCH = "domainSearch";
    /**
     * 查询域名详情(包含查询其他经销商)
     */
    public static String PUB_SEARCH = "pubSearch";
    /**
     * 查询已经适配链信息
     */
    public static String ADAPTED_SEARCHES = "adaptedSearches";

    /**
     * 域名管理者查询
     */
    public static String SEARCHES_ADMIN = "searchesAdmin";

    /**
     * ===========星级域名==============
     */
    /**
     * 星级域名查询
     */
    public static String STAR_DOMAIN_SEARCHES = "starDomainSearch";
    /**
     * 星级域名申请权购买
     */
    public static String STAR_DOMAIN_BUY = "starDomainBuy";
    /**
     * 查询购买星级域名申请权结果
     */
    public static String STAR_DOMAIN_BUY_RESULT = "starDomainBuyResult";
    /**
     * 查询星级域名等级以及申请权费用
     */
    public static String STAR_DOMAIN_LEVEL_FEE = "starDomainLevelFee";
    /**
     * 星级域名锁定、解锁
     */
    public static String STAR_DOMAIN_LOCK = "starDomainLock";
    /**
     * 星级账户信息查询
     */
    public static String STAR_ACCOUNT_SEARCH = "starAccountSearch";
    /**
     * 查询域名解析记录
     */
    public static String RESOLVE_SEARCHES = "resolveSearches";
    /**
     * 域名解析信息设置
     */
    public static String SET_RESOLVE_INFO = "setResolveInfo";
    // ===========用户注册=========

    /**
     * 注册协议
     */
    public static String REGISTRATION_AGREEMENT = "RegistrationAgreement";

    /**
     * 用户协议
     */
    public static String USER_AGREEMENT = "UserAgreement";

    /**
     * 隐私协议
     */
    public static String PRIVACY_AGREEMENT = "PrivacyAgreement";

    /**
     * did登录说明
     */
    public static String DID_LOGIN_INSTRUCTIONS = "DIDLoginInstructions";

    // ===========用户注销=========

    /**
     * 注销数据处理提示
     */
    public static String unregister_DataProcessing_Prompt = "unregisterDataProcessingPrompt";

    /**
     * 账号注销风险
     */
    public static String ACCOUNT_CANCELLATION_RISK = "accountCancellationRisk";

    /**
     *
     * 账号注销条件
     */
    public static String ACCOUNT_CANCELLATION_CONDITIONS = "accountCancellationConditions";

    /**
     * 账号注销协议
     */
    public static String ACCOUNT_CANCELLATION_AGREEMENT = "accountCancellationAgreement";

    // ==========钱包银行卡配置相关

    /**
     * 钱包用户服务协议
     */
    public static String WALLET_USER_AGREEMENT = "walletUserAgreement";

    /**
     * 个人信息保护政策
     */
    public static String PERSONAL_INFORMATION_PROTECTION_POLICY = "PersonalInformationProtectionPolicy";

    /**
     * 钱包相关协议
     */
    public static String WALLET_AGREEMENT = "walletAgreement";

    /**
     * 钱包绑定银行卡服务协议
     */
    public static String WALLET_BINDING_BANKCARD_PROTOCOL = "WalletBindingBankCardProtocol";

    /**
     * 银行卡兑换数字人民币业务服务协议
     */
    public static String Bank_Card_Exchange_Digital_Rmb_Agreement = "BankCardExchangeDigitalRmbAgreement";

    /**
     * 银行卡相关协议
     *
     */
    public static String Bank_Card_Related_Agreements = "BankCardRelatedAgreements";

    /**
     * 提现协议
     */
    public static String WITHDRAWAL_INSTRUCTIONS = "withdrawalInstructions";

    // =============域名管理相关

    /**
     * 设置域名所有者回调通知
     */
    public static String SET_DOMAIN_OWNER_NOTIFYURL = "setDomainOwnerNotifyUrl";
    /**
     * 设置域名管理者回调通知
     */
    public static String SET_DOMAIN_ADMIN_NOTIFYURL = "setDomainAdminNotifyUrl";
    /**
     * 设置域名解析记录回调通知
     */
    public static String SET_DOMAIN_RESOLVE_NOTIFYURL = "setDomainResolveNotifyUrl";
    // =========================能量充值相关============================

    /**
     * DDC业务开通凭证
     */
    public static String PROOF = "proof";
    /**
     * 文昌链定价值
     */
    public static String GAS_QUANTITY2 = "gasQuantity2";
    /**
     * 泰安链定价值
     */
    public static String GAS_QUANTITY3 = "gasQuantity3";

    /**
     * 云链能量定价值
     */
    public static String YL_GASQUANTITY = "ylGasQuantity";
    /**
     * 云链能量充值账户私钥
     */
    public static String YL_ENERGY_RECHARGE = "ylEnergyRecharge";

    /**
     * 能量二维码背景
     */
    public static String ENERGY_QR_COD_EBACKGROUND = "energyQrCodeBackground";

    /**
     * 能量充值 hex备注
     */
    public static String ENERGY_RECHARGE_HEX_REMARKS = "e883bde9878fe58585e580bc";
    // =============发票相关===========================================

    /**
     * 发票税率
     */
    public static String TAX_RATE = "tax_rate";
    /**
     * 发票内容
     */
    public static String INVOICE_CONTENT = "invoice_content";
    /**
     * 发票配置
     */
    public static String INVOICE_CONFIG = "invoice_config";
    // ================支付方式配置===============

    /**
     * 微信支付方式开关
     */
    public static String WEIXINPAYSTATE = "weiXinPayState";

    /**
     * 支付宝支付方式开关
     */
    public static String ALIPAYSTATE = "aLiPayState";
    // =======提现税率===============

    /**
     * 提现服务费
     */
    public static String WITHDRAW_SERVICE_CHARGE = "withdraw_service_charge";

    /**
     * 提现到账后的回调方法
     */
    public static final String PLAN_STATUS_CALLBACK = "plan_status_callback";

    /**
     * 签约成功后的回调
     */
    public static final String PEOPLE_STATUS_CALLBACK = "people_status_callback";
    // ==========签到===========

    /**
     * 签到周奖励
     */
    public static String SIGNINREWARD = "signInReward";
    /**
     * 签到规则
     */
    public static String SIGNINRULES = "signInRules";
    // 连续签到第一天
    public final static Integer CONTINUITE_DAY_ONE = 1;
    // 连续签到第七天
    public final static Integer CONTINUITE_DAY_SEVEN = 7;

    // 云链签到账户私钥
    public static String YL_ENERGY_SIGNIN = "ylEnergySignin";

    /**
     * 签到能量赠送备注（hex）
     */
    public static String YL_ENERGY_SIGNIN_HEX_REMARKS = "e7adbee588b0e883bde9878fe8b5a0e98081";

    /**
     * 签到修复奖励补发（hex）
     */
    public static String YL_ENERGY_SIGNIN_Reissue_HEX_REMARKS =
        "e7adbee588b0e4bfaee5a48de5a596e58ab1e8a1a5e58f91";

    /**
     * GAS price 定价
     */
    public static String GASPRICEGWI = "gasPriceGWI";
    // ==========域名转让

    /**
     * 域名转让固定费用
     */
    public final static String DOMAIN_TRANSFER_SERVICE_FEE = "domainTransferServiceFee";

    // 云链域名装让服务费收取链账户
    public static String DOMAIN_TRANSFER_SERVICE_FEE_ADDRESS = "domainTransferServiceFeeAddress";
    // ====发现模块=========

    /**
     * 发现模块背景图片配置
     */
    public static String EXPLORE_BACKGROUND = "exploreBackground";

    /**
     * 主币交易消耗的gas
     */
    public final static String FUEL_GAS = "fuelGas";

    /**
     * nft默认价格
     */
    public static final String NFT_DEFAULT_PRICE = "nftDefaultPrice";
    // ======获取基础logo

    /**
     * 灵界app logo
     */
    public static final String LJ_LOGO = "LJ_LOGO";
    /**
     * 灵界 源力 logo
     */
    public static final String LJ_Y_L = "LJ_Y_L";
    /**
     * 界外科技公司 logo
     */
    public static final String LJ_Chain_logo = "LJ_Chain_logo";

    // =====任务中心

    public static final String SQRM30 = "浏览社区热门动态30s";

    public static final String SQZX30 = "浏览社区最新动态30s";

    public static final String FBYTDT = "发布一条动态/评论/回复";
    public static final String FXYTDT = "分享一条动态";
    public static final String DZYTDT = "点赞一条动态";
    public static final String FBSPDT = "发布视频动态";
    public static final String CZZXWCRYGM = "充值中心完成任一购买";
    public static final String GMYM = "购买域名";
    public static final String YYGH = "预约挂号";
    public static final String GYRW = "观影任务";

    // 周期任务
    public static final String LJFB10DT = "累计发布10条动态";
    public static final String LJHD30GDZ = "累计获得30个点赞";
    public static final String XZ30GFS = "新增30个粉丝";
    public static final String CGJY4CYM = "成功交易4次域名";

    // 新手任务
    public static final String SZYMMC = "设置域名名称";
    public static final String SZNFTTX = "首次设置NFT头像";
    public static final String SCGZHY = "首次关注好友";
    public static final String SCHDFS = "首次获得粉丝";
    public static final String SCHDDZ = "首次获得点赞";
    public static final String SCFBSPDD = "首次发布视频动态";

    /**
     * 限时任务周期
     */
    public static final String TIME_LIMITED_TASKCYCLE = "timeLimitedTaskCycle";

    /**
     * 社区任务奖励（hex）
     */
    public static String TASK_REWARDS_HEX_REMARKS = "e7a4bee58cbae4bbbbe58aa1e5a596e58ab1";

    /**
     * 任务变动消息推送
     *
     */
    public static String TASK_CHANGE_NOTIFICATION = "TaskChangeNotification";

    // =====源力概览

    /**
     * 总量SP
     */
    public static String YL_SP_Total = "YLSPTotal";

    /**
     * 收益百分比
     */
    public static String REVENUE_PERCENTAGE = "REVENUE_PERCENTAGE";

    // ===授权登录

    /**
     *
     * 授权登录过期时间
     */
    public static String AUTHORIZATION_EXPIRATION_TIME = "authorizationExpirationTime";

    /**
     * NFT抵扣续费域名,授权平台地址
     */
    public static String NFT_RENEWAL_DOMAIN_ACCREDIT_ADDRESS = "nft_renewal_domain_accredit_address";

    /**
     * NFT抵扣续费域名,回收/接收平台地址
     */
    public static String NFT_RENEWAL_DOMAIN_RECYCLE_ADDRESS = "nft_renewal_domain_recycle_address";

    // ====贡献支付配置

    /**
     * 微信支付方式开关
     */
    public static String GX_WEIXINPAYSTATE = "GXweiXinPayState";

    /**
     * 支付宝支付方式开关
     */
    public static String GX_ALIPAYSTATE = "GXaLiPayState";

    /**
     * 抖音支付方式开关
     */
    public static String GX_DOUYINPAYSTATE = "GXdouyinPayState";

    /**
     * 贡献模块开关
     */
    public static String CONTRIBUTION_SWITCH = "contributionSwitch";

    /**
     * NFT 领取失败原因
     */
    public static String NFT_CLAIM_FAILURE_REASONS = "nft_claim_failure_Reasons";

    // DID二维码扫码

    /**
     * did 二维码过期时间
     */
    public static String DID_QR_CODE_EXPIRATION_TIME = "didQrCodeExpirationTime";

    /**
     * DID 二维码信息展示开关
     */
    public static String did_Qr_Code_Switch = "didQrCodeSwitch";

    // 实名DID小程序授权过期时间

    public static String DID_MINI_MESSAGE_EXPIRATION_TIME = "didMiniMessageExpirationTime";

    public static final Integer DEFAULT_PAGE = 1;

    public static final Integer DEFAULT_PAGE_SIZE = 10;

    /**
     * 支付失效时间(分钟)
     */
    public static final Integer PAYMENT_TIME_LIMIT = 15;

    /**
     * 默认坐标
     */
    public static final String DEFAULT_COORDINATE = "default_coordinate";

    /**
     * 同步im信息开关
     **/
    public static final String SYNC_IM_INFO_SWITCH = "sync_im_info_switch";

    /**
     * 同步客服信息开关
     **/
    public static final String SYNC_IM_STAFF_SWITCH = "sync_im_staff_switch";

    /**
     * 推广did标识图片
     */
    public static final String TG_DID_SIGN_IMAGE = "tg_did_sign_image";

    /**
     *
     * 推广did标识图片(无)
     */
    public static final String TG_DID_SIGN_IMAGE_H = "tg_did_sign_image_h";

    /**
     *
     * 推广裂空者标识图片
     */
    public static final String TG_LKZ_SIGN_IMAGE = "tg_lkz_sign_image";

    /**
     * 推广裂空者标识图片(无效)
     */
    public static final String TG_LKZ_SIGN_IMAGE_W = "tg_lkz_sign_image_w";

    /**
     * 推广列表无效裂空者温馨提示
     */
    public static final String TG_LKZ_PROMPT = "tg_lkz_prompt";

    /**
     * 域名标识图片
     */
    public static final String DOMAINNICKNAMESIGNIMAGE = "domainNickNameSignImage";

    /**
     * 推广大使等级图片
     */
    public static final String EXITAMBASSADORLEVELPICTURE = "exitAmbassadorLevelPicture";

    /**
     * 首页签到弹窗图片
     */
    public static final String CHECKINHOMEPAGEREMINDER = "checkInHomepageReminder";

    /**
     * 徽章
     */
    public static final String BADGE = "badge";

    /**
     * 挂件
     */
    public static final String AVATAR = "avatar";

    /**
     * 域名昵称徽章
     */
    public static final String DOMAIN_NAME_BADGE = "域名昵称";

    /**
     * 推广大使徽章
     */
    public static final String PROMOTION_AMBASSADOR_BADGE = "推广大使";

    /**
     * 裂空者徽章
     */
    public static final String SKY_SPLIT_BADGE = "裂空者";

    /**
     * 裂空者徽章
     */
    public static final String NFT = "NFT头像";

    /**
     * 域名首次体验
     */
    public static final String FIRST_EXPERIENCEOF_DOMAINNAME = "firstExperienceOfDomainName";

    /**
     * 域名体验到期
     */
    public static final String DOMAIN_EXPERIENCE_EXPIRES = "domainExperienceExpires";

    /**
     * 域名体验已注册
     */
    public static final String DOMAIN_EXPERIENCE_REGISTERED = "domainExperienceRegistered";

    /**
     * 用户基础信息
     */
    public static final String USER_BASIC_INFORMATION = "userBasicInformation";
}

package com.lj.auth.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.Chain;
import com.lj.auth.domain.SignInHist;
import com.lj.auth.domain.vo.SignInHistVo;
import com.lj.auth.domain.vo.SignInVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.mapper.AccountMapper;
import com.lj.auth.mapper.ChainMapper;
import com.lj.auth.mapper.SignInHistMapper;
import com.lj.auth.service.*;
import com.lj.auth.util.BNBUtil;
import com.lj.auth.util.BaseConversionUtils;
import com.lj.auth.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.SignIn;
import com.lj.auth.mapper.SignInMapper;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.lj.auth.common.CommonConstant.*;

@Service
@Slf4j
public class SignInServiceImpl extends ServiceImpl<SignInMapper, SignIn> implements SignInService {
    @Resource
    private AccountService accountService;
    @Resource
    private SignInHistMapper signInHistMapper;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private TransferNoticeService transferNoticeService;
    @Resource
    private ChainMapper chainMapper;
    @Resource
    private EnergyRechargeFlowService energyRechargeFlowService;
    @Value("${ylChainId}")
    private Integer chainId;

    /**
     * 查询签到记录
     *
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R signInDetailService(Integer page, Integer pageSize) {
        Account userInfo = accountService.getUserInfo();
        Integer totalCount = signInHistMapper
            .selectCount(Wrappers.<SignInHist>lambdaQuery().eq(SignInHist::getAccountUuid, userInfo.getUuid()));
        int start = (page - 1) * pageSize;
        List<SignInHist> noticeList = new ArrayList<>();
        if (start < totalCount) {
            noticeList = signInHistMapper.selectList(
                Wrappers.<SignInHist>lambdaQuery().eq(SignInHist::getAccountUuid, userInfo.getUuid())
                    .orderByDesc(SignInHist::getSignInDate).last("limit" + " " + start + "," + pageSize));
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, noticeList);
        return R.okData(pageUtils);
    }

    /**
     * 查询签到周期纪录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R weeklyCheckInRecordService() {
         Account userInfo = accountService.getUserInfo();
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> result1 = new HashMap<>();
        List<SignInVo> signInVos = new ArrayList<>();
        // 签到周期奖励
        List<BigDecimal> signInRewardList = globalConfigService.getSignGlobalConfig(SIGNINREWARD);
        // 签到周期
        int checkInCycle = signInRewardList.size();
        // 获取最新一条签到记录
        SignIn one = this.getOne(Wrappers.<SignIn>lambdaQuery().eq(SignIn::getAccountUuid, userInfo.getUuid())
                .orderByDesc(SignIn::getSignInDate).last("limit 1"));
        if (one != null) {
            // 判断是否断签
            long differenceDays = DateUtil.between(one.getSignInDate(), DateUtil.date(), DateUnit.DAY);
            // 连续签到天数
            Integer continuiteDay = one.getContinuiteDay();
            if (differenceDays > 1) {
                result1.put("isInterrupt", false);
                result1.put("continuiteDay", continuiteDay);
                result1.put("rewardMoney",
                    signInRewardList.stream().limit(continuiteDay).reduce(BigDecimal.ZERO,BigDecimal::add));
            }
            Date signInDate = one.getSignInDate();
            boolean isToday = DateUtil.isSameDay(signInDate, DateUtil.date());
            // 判断日期是否是今天is
            if (isToday) {
                // 今天是否签到 1 --签到 0--未签到
                result.put("SignIn", 1);
                parameterAssembly(signInVos, signInRewardList, userInfo, one);
                // 连续签到天数
                result.put("continuousCheckInDays", continuiteDay);
                // 还差几天获得连续签到奖励 0 为已经获得连续签到奖励
                result.put("continuousCheckInReward", checkInCycle - continuiteDay);
                // 签到记录
                result.put("attendanceRecord", signInVos);
                // 开始周期
                result.put("startOfCycle", signInVos.get(0).getCycleDate());
                // 结束周期
                result.put("endOfCycle", signInVos.get(checkInCycle - 1).getCycleDate());
                // 不是今天
            } else {
                // 今天是否签到 1 --签到 0--未签到
                result.put("SignIn", 0);
                // 昨天日期
                Date dateTime = DateUtil.beginOfDay(DateUtil.yesterday());
                boolean isYesterday = DateUtil.isSameDay(signInDate, dateTime);
                // 判断是否是昨天
                if (isYesterday) {
                    // 今天是否签到 1 --签到 0--未签到
                    parameterAssembly(signInVos, signInRewardList, userInfo, one);
                    // 连续签到天数
                    result.put("continuousCheckInDays", continuiteDay);
                    // 还差几天获得连续签到奖励 0 为已经获得连续签到奖励
                    result.put("continuousCheckInReward", checkInCycle - continuiteDay);
                    // 签到记录
                    result.put("attendanceRecord", signInVos);
                    // 开始周期
                    result.put("startOfCycle", signInVos.get(0).getCycleDate());
                    // 结束周期
                    result.put("endOfCycle", signInVos.get(checkInCycle - 1).getCycleDate());
                } else {
                    // 今天是否签到 1 --签到 0--未签到
                    // 不是昨天 就是断签了 今天就是第一天
                    parameterAssembly1(signInVos, signInRewardList);
                    // 连续签到天数
                    result.put("continuousCheckInDays", 0);
                    // 还差几天获得连续签到奖励 0 为已经获得连续签到奖励
                    result.put("continuousCheckInReward", 7);
                    // 签到记录
                    result.put("attendanceRecord", signInVos);
                    // 开始周期
                    result.put("startOfCycle", signInVos.get(0).getCycleDate());
                    // 结束周期
                    result.put("endOfCycle", signInVos.get(checkInCycle - 1).getCycleDate());
                }
            }
        } else {
            // 今天是否签到 1 --签到 0--未签到
            // 不是昨天 就是断签了 今天就是第一天
            parameterAssembly1(signInVos, signInRewardList);
            result.put("SignIn", 0);
            // 连续签到天数
            result.put("continuousCheckInDays", 0);
            // 还差几天获得连续签到奖励 0 为已经获得连续签到奖励
            result.put("continuousCheckInReward", 7);
            // 签到记录
            result.put("attendanceRecord", signInVos);
            // 开始周期
            result.put("startOfCycle", signInVos.get(0).getCycleDate());
            // 结束周期
            result.put("endOfCycle", signInVos.get(checkInCycle - 1).getCycleDate());
        }
        result.put("interrupt", result1);
        // 封装签到弹窗照片
        result.put("signInPicture",globalConfigService.getGlobalConfig(CHECKINHOMEPAGEREMINDER));
        return R.okData(result);
    }

    /**
     * 签到
     * 
     * @param address 链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    @Transactional
    public R signInService(String address) {
        Account userInfo = accountService.getUserInfo();
        List<BigDecimal> signInRewardList = globalConfigService.getSignGlobalConfig(SIGNINREWARD);
        String accountUUID = userInfo.getUuid();
        //校验用户有没有DID
        if (StrUtil.isEmpty(userInfo.getDidSymbol())) {
            return R.error(ResponseEnum.NotDIDCertifiedPlease);
        }
        SignIn signIn = this.getOne(Wrappers.<SignIn>lambdaQuery().eq(SignIn::getAccountUuid, accountUUID)
            .orderByDesc(SignIn::getSignInDate).last("limit 1"));
        //判断签到地址是否被他人使用过
        int count = this.count(Wrappers.<SignIn>lambdaQuery().eq(SignIn::getAddress, address).ne(SignIn::getAccountUuid, accountUUID));
        if (count > 0) {
            return R.error(ResponseEnum.AddressOccupation);
        }
        Map<String, Object> result = new HashMap<>();
        // 签到记录是否为空
        if (null == signIn) {
            SignIn signOne = new SignIn();
            signOne.setAddress(address);
            signOne.setAccountUuid(accountUUID);
            signOne.setContinuiteDay(CONTINUITE_DAY_ONE);
            signOne.setRewardMoney(signInRewardList.get(0));
            signOne.setSignInDate(DateUtil.beginOfDay(new Date()));
            // 签到能量赠送
            // String hash = energyRechargeFlowService.transactionMainCurrency(YL_ENERGY_SIGNIN, address,
            // signInRewardList.get(0), 3, YL_ENERGY_SIGNIN_HEX_REMARKS);
            // log.info("签到能量赠送hash:" + hash);
            // log.info("签到能量赠送hash:" + hash);
            // log.info("签到能量赠送hash:" + hash);
            signOne.setHash(null);
            insertSigninAndHist(signOne);

            // 能量 转账通知
            // String content = "赠送能量:" + signInRewardList.get(0) + "到地址:" + address;
            // transferNoticeService.addUserNoticeMessage(accountUUID, "签到能量赠送", content, "签到赠送能量");

            result.put("rewardMoney", signInRewardList.get(0));
            result.put("continuiteDay", 1);
            result.put("continuousCheckInReward", 6);
            return R.okData(result);
        }

        long signInDateTime = signIn.getSignInDate().getTime();
        if (signInDateTime == DateUtil.beginOfDay(new Date()).getTime()) {
            return R.ok("今天已经签到");
        }
        // 获取连续签到天数
        Integer days = continuiteDay(signIn.getContinuiteDay(), signInDateTime);
        signIn.setSignInDate(DateUtil.beginOfDay(new Date()));
        signIn.setContinuiteDay(days);
        signIn.setRewardMoney(signInRewardList.get(days - 1));
        signIn.setUpdateTime(new Date());
        signIn.setAddress(address);
        signIn.setState(0);
        // 签到能量赠送
        // String hash = energyRechargeFlowService.transactionMainCurrency(YL_ENERGY_SIGNIN, address,
        // signInRewardList.get(days - 1), 3, YL_ENERGY_SIGNIN_HEX_REMARKS);
        // log.info("签到赠送hash：{} ", hash);
        signIn.setHash(null);
        updateSignInAndInsertHist(signIn);
        // 能量 转账通知
        // String content = "赠送能量:" + signInRewardList.get(days - 1) + "到地址:" + address;
        // transferNoticeService.addUserNoticeMessage(accountUUID, "签到能量赠送", content, "签到赠送能量");

        result.put("rewardMoney", signInRewardList.get(days - 1));
        result.put("continuiteDay", days);
        result.put("continuousCheckInReward", signInRewardList.size() - days);
        return R.okData(result);
    }

    /**
     * 签到规则
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R signInRulesService() {
        return R.okData(globalConfigService.getGlobalConfig(SIGNINRULES));
    }

    /**
     * 签到记录详情
     * 
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @Override
    public R signInDetailRecordService(Integer id) {
        SignInHist signInHist = signInHistMapper.selectById(id);
        Chain chain = chainMapper.selectOne(Wrappers.<Chain>lambdaQuery().eq(Chain::getChainId, chainId));
        String hash = signInHist.getHash();
        BigInteger block = new BigInteger("0");
        Integer state = 1;
        if (StrUtil.isBlank(hash)) {
            state = 0;
        } else {
            block = BNBUtil.getBlockNumber(hash);
        }
        SignInHistVo signInHistVo = new SignInHistVo();
        signInHistVo.setTime(signInHist.getUpdateTime()).setState(state).setChainLogo(chain.getChainLogo())
            .setChainName(chain.getChainName()).setReceiveAddress(signInHist.getAddress()).setFee("0")
            .setHash(hash).setBlock(block);

        return R.okData(signInHistVo);
    }
    // ========工具方法===================================

    /**
     * @param signInVos
     * @param signInRewardList
     * @param ylChainAccount
     * @param walletUserInfo
     * @param one
     */
    private void parameterAssembly1(List<SignInVo> signInVos, List<BigDecimal> signInRewardList) {
        for (int i = 0; i < signInRewardList.size(); i++) {
            SignInVo sign = new SignInVo();
            sign.setRewardMoney(signInRewardList.get(i));
            sign.setDays(i + 1);
            sign.setIsSign(false);
            sign.setCycleDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), i)));
            signInVos.add(sign);
        }
    }

    /**
     * @param signInVos
     * @param signInRewardList
     * @param ylChainAccount
     * @param walletUserInfo
     * @param one
     * @return {@link List}<{@link SignInVo}>
     */
    private void parameterAssembly(List<SignInVo> signInVos, List<BigDecimal> signInRewardList,
        Account userInfo, SignIn one) {
        // 查询连续签到记录
        List<SignInHist> signInHists = signInHistMapper
            .selectList(Wrappers.<SignInHist>lambdaQuery().eq(SignInHist::getAccountUuid, userInfo.getUuid())
                .orderByDesc(SignInHist::getSignInDate).last("limit" + " " + one.getContinuiteDay()));
        Collections.sort(signInHists, Comparator.comparing(SignInHist::getSignInDate));
        int size = signInHists.size();
        for (int i = 0; i < signInRewardList.size(); i++) {
            SignInVo sign = new SignInVo();
            if (i < size) {
                SignInHist signInHist = signInHists.get(i);
                sign.setSignInDate(signInHist.getSignInDate());
                sign.setAccountUuid(signInHist.getAccountUuid());
                sign.setContinuiteDay(signInHist.getContinuiteDay());
                sign.setAddress(signInHist.getAddress());
                sign.setRewardMoney(signInHist.getRewardMoney());
                sign.setIsSign(true);
            } else {
                sign.setRewardMoney(signInRewardList.get(i));
                sign.setIsSign(false);
            }
            sign.setDays(i + 1);
            sign.setCycleDate(DateUtil.beginOfDay(DateUtil.offsetDay(signInHists.get(0).getSignInDate(), i)));
            signInVos.add(sign);
        }
    }

    private void insertSigninAndHist(SignIn sign) {
        this.save(sign);
        SignInHist signInHist = new SignInHist();
        BeanUtils.copyProperties(sign, signInHist);
        signInHistMapper.insert(signInHist);
    }

    private void updateSignInAndInsertHist(SignIn sign) {
        this.updateById(sign);
        SignInHist signInHist = new SignInHist();
        BeanUtils.copyProperties(sign, signInHist);
        signInHistMapper.insert(signInHist);
    }

    private Integer continuiteDay(Integer continuiteDay, Long signInDateTime) {
        if (signInDateTime < DateUtil.beginOfDay(DateUtil.yesterday()).getTime()) {
            return CONTINUITE_DAY_ONE;
        }
        if (continuiteDay >= CONTINUITE_DAY_SEVEN) {
            return CONTINUITE_DAY_ONE;
        }
        return continuiteDay + 1;
    }
}

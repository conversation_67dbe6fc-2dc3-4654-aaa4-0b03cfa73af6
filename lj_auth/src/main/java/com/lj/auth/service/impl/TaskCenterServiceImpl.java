package com.lj.auth.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.*;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.service.TaskCenterUserService;
import com.lj.auth.util.*;
import jnr.ffi.annotations.In;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.R;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.TaskCenterService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import static com.lj.auth.common.CommonConstant.*;

/**
 * <p>
 * 任务中心 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Service
@Slf4j
public class TaskCenterServiceImpl extends ServiceImpl<TaskCenterMapper, TaskCenter>
    implements TaskCenterService {

    private final Map<String, ReentrantLock> userLocks = new ConcurrentHashMap<>();
    @Value("${ylChainId}")
    private Integer chainId;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private AccountService accountService;
    @Resource
    private TaskCenterMapper taskCenterMapper;
    @Resource
    private TaskCenterUserMapper taskCenterUserMapper;
    @Resource
    private TaskCenterUserService taskCenterUserService;
    @Resource
    private TaskTypeMapper taskTypeMapper;
    @Resource
    private TrendsMapper trendsMapper;
    @Resource
    private Executor executor;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private FollowMapper followMapper;
    @Resource
    private CommentReplyMapper commentReplyMapper;
    @Resource
    private TrendsForwardMapper trendsForwardMapper;
    @Resource
    private TrendsLikesMapper trendsLikesMapper;
    @Resource
    private CommentMapper commentMapper;
    @Resource
    private ChainMapper chainMapper;
    @Resource
    private SignInHistMapper signInHistMapper;
    @Resource
    private SignInMapper signInMapper;
    @Resource
    private DomainTransferRecordMapper domainTransferRecordMapper;

    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private RemindMapper remindMapper;
    @Resource
    private LifeRechargeRecordMapper lifeRechargeRecordMapper;
    @Resource
    private DomainAccountMapper domainAccountMapper;
    @Resource
    private MixOrderMapper mixOrderMapper;
    @Resource
    private MixOrderViewMapper mixOrderViewMapper;
    @Resource
    private MovieOrderMapper movieOrderMapper;
    @Resource
    private ModuleSwitchMapper  moduleSwitchMapper;

    @Override
    public R statisticalTasksService() {
        try {
            Account userInfo = accountService.getUserInfo();
            Boolean aBoolean = checkRefreshCount(userInfo);
            if (!aBoolean) {
                return R.ok();
            }
            // 获取为关闭的任务
            List<TaskCenter> taskCenters = taskCenterMapper
                .selectList(Wrappers.<TaskCenter>lambdaQuery().eq(TaskCenter::getTaskState, 1));
            // 用于存储每个任务的Future，方便后续管理
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            // 遍历任务列表，提交任务到线程池
            for (TaskCenter taskCenter : taskCenters) {
//                log.info(taskCenter.getTaskName() + "任务开始");
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    executeTask(taskCenter, userInfo);
                }, executor);
                futures.add(future);
            }
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("统计任务异常", e);
        }
        return R.ok();
    }

    /**
     * 获取限时任务列表
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    @Override
    public R getLimitedTimeTaskService() {
        // 组装返回参数
        List<TaskCenterUserVo> result = new ArrayList<>();
        // 获取用户信息
        Account userInfo = accountService.getUserInfo();
        // 获取任务中心限时任务==>未关闭且 开始时间小与当前时间 结束时间大于当前时间
        List<TaskCenter> taskCenters = taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery()
            .eq(TaskCenter::getTaskTypeId, 4).eq(TaskCenter::getTaskState, 1)
            .le(TaskCenter::getOpeningTime, new Date()).ge(TaskCenter::getCompletionTime, new Date()));
        // 如果没有限时任务返回为空
        if (CollectionUtil.isEmpty(taskCenters)) {
            return R.okData(result);
        }
        List<Integer> taskCenterIds =
            taskCenters.stream().map(TaskCenter::getId).collect(Collectors.toList());
        // 获取个人限时任务完成情况
        List<TaskCenterUser> taskCenterUsers = taskCenterUserMapper.selectList(
            Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                .in(TaskCenterUser::getTaskCenterId, taskCenterIds));
        // 更具任务中心任务匹配 个人任务完成清况
        for (TaskCenter taskCenter : taskCenters) {
            TaskCenterUserVo taskCenterUserVo = new TaskCenterUserVo();
            taskCenterUserVo.setTaskTypeId(taskCenter.getTaskTypeId()).setTaskName(taskCenter.getTaskName())
                .setIntroduce(taskCenter.getIntroduce()).setReward(taskCenter.getReward())
                .setCompletionCount(taskCenter.getCompletionCount())
                .setOpeningTime(taskCenter.getOpeningTime()).setIsComplete(0)
                .setCompletionTime(taskCenter.getCompletionTime());
            if (CollectionUtil.isNotEmpty(taskCenterUsers)) {
                for (TaskCenterUser taskCenterUser : taskCenterUsers) {
                    if (ObjectUtil.equals(taskCenter.getId(), taskCenterUser.getTaskCenterId())) {
                        taskCenterUserVo.setId(taskCenterUser.getId())
                            .setAccountUuid(taskCenterUser.getAccountUuid())
                            .setTaskCenterId(taskCenterUser.getTaskCenterId())
                            .setTaskIdCompleted(taskCenterUser.getTaskIdCompleted())
                            .setCompleteReward(taskCenterUser.getReward())
                            .setIsComplete(taskCenterUser.getIsComplete())
                            .setIsReceive(taskCenterUser.getIsReceive()).setHash(taskCenterUser.getHash())
                            .setState(taskCenterUser.getState())
                            .setActualTaskCompletionTime(taskCenterUser.getCompletionTime())
                            .setActualCompletedQuantity(taskCenterUser.getCompletionCount())
                            .setCreateTime(taskCenterUser.getCreateTime())
                            .setUpdateTime(taskCenterUser.getUpdateTime());
                    }
                }
            }
            result.add(taskCenterUserVo);
        }
        return R.okData(result);
    }

    /**
     * 获取任务类型
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    @Override
    public R getCenterTypeService() {
        List<TaskTypeVo> result = taskTypeMapper.getCenterTypeList();
        return R.okData(result);
    }

    /**
     * 日常 周期 新手 任务列表展示
     *
     * @param request
     * @param taskTypeId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/29
     */
    @Override
    public R getTaskListService(HttpServletRequest request,Integer taskTypeId) {
        String channel = request.getHeader("channel");
        if (StringUtils.isEmpty(channel)) {
            channel = "JIEWAI";
        }
        // 组装返回参数
        List<TaskCenterUserVo> result = new ArrayList<>();
        // 获取用户信息
        Account userInfo = accountService.getUserInfo();
        List<TaskCenter> taskCenters = new ArrayList<>();
        // 新手任务
        if (ObjectUtil.equals(taskTypeId, 3)) {
            // 获取任务中心新手任务==>未关闭且 开始时间小与当前时间 结束时间不限制 只能完成一次
            taskCenters = taskCenterMapper
                .selectList(Wrappers.<TaskCenter>lambdaQuery().eq(TaskCenter::getTaskTypeId, taskTypeId)
                    .eq(TaskCenter::getTaskState, 1).le(TaskCenter::getOpeningTime, new Date()));
            // 日常任务=>每天动态重置 开始时间小于等于当天凌晨 结束时间大于等于当天结束时间
        } else if (ObjectUtil.equals(taskTypeId, 1)) {
            taskCenters = taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery()
                .eq(TaskCenter::getTaskTypeId, taskTypeId).eq(TaskCenter::getTaskState, 1)
                .le(TaskCenter::getOpeningTime, DateUtil.formatDateTime(DateUtil.beginOfDay(new Date())))
                .ge(TaskCenter::getCompletionTime, DateUtil.formatDateTime(DateUtil.endOfDay(new Date()))));
            // 周期任务=>每周重置一次
        } else if (ObjectUtil.equals(taskTypeId, 2)) {
            Date currentDate = DateUtil.date();
            // 获取本周周一的日期，即本周开始时间
            Date weekStartDate = DateUtil.beginOfWeek(currentDate);
            String weekStart = DateUtil.formatDateTime(weekStartDate);
            // 本周周日结束时间
            Date weekEndDate = DateUtil.endOfWeek(currentDate);
            String weekEnd = DateUtil.formatDateTime(weekEndDate);
            taskCenters = taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery()
                .eq(TaskCenter::getTaskTypeId, taskTypeId).eq(TaskCenter::getTaskState, 1)
                .le(TaskCenter::getOpeningTime, weekStart).ge(TaskCenter::getCompletionTime, weekEnd));
        }
        // 如果没有返回空
        if (CollectionUtil.isEmpty(taskCenters)) {
            return R.okData(result);
        }

        // 更具特定的版本返回指定任务
        // 获取要移除的任务
        List<ModuleSwitch> moduleSwitches =
            moduleSwitchMapper.selectList(Wrappers.<ModuleSwitch>lambdaQuery().eq(ModuleSwitch::getTypeId, 1)
                .eq(ModuleSwitch::getChannel, channel).eq(ModuleSwitch::getValue, false));
        if (CollectionUtil.isNotEmpty(moduleSwitches)) {
            // 过滤掉taskCenters中Describe和moduleSwitches中key相同的任务
            taskCenters.removeIf(taskCenter -> {
                return moduleSwitches.stream()
                    .anyMatch(moduleSwitch -> moduleSwitch.getKey().equals(taskCenter.getDescribe()));
            });
        }
        
        List<Integer> taskCenterIds =
            taskCenters.stream().map(TaskCenter::getId).collect(Collectors.toList());
        // 获取个人任务完成情况
        List<TaskCenterUser> taskCenterUsers = taskCenterUserMapper.selectList(
            Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                .in(TaskCenterUser::getTaskCenterId, taskCenterIds));

        // 更具任务中心任务匹配 个人任务完成清况
        for (TaskCenter taskCenter : taskCenters) {
            String introduce = taskCenter.getDescribe();
            TaskCenterUserVo taskCenterUserVo = new TaskCenterUserVo();

            taskCenterUserVo.setTaskTypeId(taskCenter.getTaskTypeId()).setTaskName(taskCenter.getTaskName())
                .setIntroduce(taskCenter.getIntroduce()).setReward(taskCenter.getReward())
                .setCompletionCount(taskCenter.getCompletionCount())
                .setOpeningTime(taskCenter.getOpeningTime()).setIsComplete(0)
                .setCompletionTime(taskCenter.getCompletionTime());

            // 可重复计算的任务需单独处理奖励 电影任务奖励可以重复计算
            BigDecimal movieReward = BigDecimal.ZERO;

            if (CollectionUtil.isNotEmpty(taskCenterUsers)) {
                for (TaskCenterUser taskCenterUser : taskCenterUsers) {
                    if (ObjectUtil.equals(introduce, GYRW)) {
                        if (ObjectUtil.equals(taskCenter.getId(), taskCenterUser.getTaskCenterId())
                            && ObjectUtil.equals(taskCenterUser.getIsReceive(), 0)) {
                            taskCenterUserVo.setId(taskCenterUser.getId())
                                .setAccountUuid(taskCenterUser.getAccountUuid())
                                .setTaskCenterId(taskCenterUser.getTaskCenterId())
                                .setTaskIdCompleted(taskCenterUser.getTaskIdCompleted())
                                .setCompleteReward(taskCenterUser.getReward())
                                .setIsComplete(taskCenterUser.getIsComplete())
                                .setIsReceive(taskCenterUser.getIsReceive()).setHash(taskCenterUser.getHash())
                                .setState(taskCenterUser.getState())
                                .setActualTaskCompletionTime(taskCenterUser.getCompletionTime())
                                .setActualCompletedQuantity(taskCenterUser.getCompletionCount())
                                .setCreateTime(taskCenterUser.getCreateTime())
                                .setUpdateTime(taskCenterUser.getUpdateTime());
                            movieReward = movieReward.add(taskCenterUser.getReward());
                        }
                    } else {
                        if (ObjectUtil.equals(taskCenter.getId(), taskCenterUser.getTaskCenterId())) {
                            taskCenterUserVo.setId(taskCenterUser.getId())
                                .setAccountUuid(taskCenterUser.getAccountUuid())
                                .setTaskCenterId(taskCenterUser.getTaskCenterId())
                                .setTaskIdCompleted(taskCenterUser.getTaskIdCompleted())
                                .setCompleteReward(taskCenterUser.getReward())
                                .setIsComplete(taskCenterUser.getIsComplete())
                                .setIsReceive(taskCenterUser.getIsReceive()).setHash(taskCenterUser.getHash())
                                .setState(taskCenterUser.getState())
                                .setActualTaskCompletionTime(taskCenterUser.getCompletionTime())
                                .setActualCompletedQuantity(taskCenterUser.getCompletionCount())
                                .setCreateTime(taskCenterUser.getCreateTime())
                                .setUpdateTime(taskCenterUser.getUpdateTime());
                        }
                    }

                }
            }
            if (ObjectUtil.equals(introduce, GYRW)) {
                taskCenterUserVo
                    .setReward(movieReward.equals(BigDecimal.ZERO) ? taskCenter.getReward() : movieReward);
                taskCenterUserVo.setCompleteReward(movieReward);
            }
            result.add(taskCenterUserVo);
        }

        return R.okData(result);
    }

    /**
     * 邀请用户列表展示
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/29
     */
    @Override
    public R getNewUserTaskListService(Integer page, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        // 获取用户信息
        Account userInfo = accountService.getUserInfo();
        // 获取任务中邀请用户任务==>未关闭且 开始时间小与当前时间
        List<TaskCenter> taskCenters =
            taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery().eq(TaskCenter::getTaskTypeId, 5)
                .eq(TaskCenter::getTaskState, 1).le(TaskCenter::getOpeningTime, new Date()));
        // 如果没有邀请用户任务返回为空
        if (CollectionUtil.isEmpty(taskCenters)) {
            return R.okData(result);
        }
        List<Integer> taskCenterIds =
            taskCenters.stream().map(TaskCenter::getId).collect(Collectors.toList());

        Integer totalCount = taskCenterUserMapper.queryTaskUserCount(userInfo.getUuid(), taskCenterIds);

        int start = (page - 1) * pageSize;
        List<TaskUserVo> taskUserVos =
            taskCenterUserMapper.getTaskUserPage(start, pageSize, userInfo.getUuid(), taskCenterIds);

        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, taskUserVos);
        // 任务信息
        TaskCenter taskCenter = taskCenters.get(0);
        result.put("taskName", taskCenter.getTaskName());
        result.put("introduce", taskCenter.getIntroduce());
        // 累计获得SP 统计
        BigDecimal totalNumSP = taskCenterUserMapper.getTotalSPNum(userInfo.getUuid(), taskCenterIds, 1);
        // 待领取sp
        BigDecimal notClaimedSP = taskCenterUserMapper.getTotalSPNum(userInfo.getUuid(), taskCenterIds, 0);

        result.put("totalNumSP", totalNumSP);
        result.put("notClaimedSP", notClaimedSP);
        // 邀请用户列表展示
        result.put("pageList", pageUtils);
        return R.okData(result);
    }

    /**
     * 领取 SP
     * 
     * @param id -任务id
     * @param address-领取地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/31
     */
    @Override
    public R getReceiceService(Integer id, String address) {
        // 获取用户信息
        Account userInfo = accountService.getUserInfo();
        // 校验用户有没有DID
        if (StrUtil.isEmpty(userInfo.getDidSymbol())) {
            return R.error(ResponseEnum.NotDIDCertifiedPlease);
        }
        // 获取完成任务信息
        TaskCenterUser taskCenterUser = taskCenterUserMapper.selectById(id);
        //判断任务地址是否被他人使用过
        int count = taskCenterUserMapper.selectCount(Wrappers.<TaskCenterUser>lambdaQuery()
            .eq(TaskCenterUser::getAddress, address).ne(TaskCenterUser::getAccountUuid, userInfo.getUuid()));
        if (count > 0) {
            return R.error(ResponseEnum.AddressOccupation);
        }
        TaskCenter taskCenter = taskCenterMapper.selectById(taskCenterUser.getTaskCenterId());
        // 判断是否是重复完成的任务 比如观影任务 ，这类任务如果领取 则是全部都领取
        String introduce = taskCenter.getDescribe();
        if (ObjectUtil.equals(introduce, GYRW)) {
            // 查询 已完成 任务未领取的 全部改成领取
            List<TaskCenterUser> taskCenterUsers = taskCenterUserMapper.selectList(
                Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .eq(TaskCenterUser::getTaskCenterId, taskCenterUser.getTaskCenterId())
                    .eq(TaskCenterUser::getIsComplete, 1).eq(TaskCenterUser::getIsReceive, 0));
            if (CollectionUtil.isNotEmpty(taskCenterUsers)) {
                // 批量修改状态和领取地址
                taskCenterUserMapper.update(null,
                    Wrappers.<TaskCenterUser>lambdaUpdate()
                        .in(TaskCenterUser::getId,
                            taskCenterUsers.stream().map(TaskCenterUser::getId).collect(Collectors.toList()))
                        .set(TaskCenterUser::getAddress, address).set(TaskCenterUser::getIsReceive, 1));
            }
        } else {
            // 判断任务是否完成
            Integer isComplete = taskCenterUser.getIsComplete();
            // 判断是否已经领取
            Integer isReceive = taskCenterUser.getIsReceive();
            if (ObjectUtil.equals(isReceive, 0) && ObjectUtil.equals(isComplete, 1)) {
                // 修改领取状态
                taskCenterUserMapper.update(null,
                    Wrappers.<TaskCenterUser>lambdaUpdate().eq(TaskCenterUser::getId, id)
                        .set(TaskCenterUser::getAddress, address)
                        .set(TaskCenterUser::getReward, taskCenter.getReward())
                        .set(TaskCenterUser::getIsReceive, 1));
            }

        }
        return R.ok();
    }

    /**
     * 一键领取sp=>只限于长期任务（邀请新用户）
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @Override
    public R getOneClickClaimService(String address) {
        // 获取用户信息
        Account userInfo = accountService.getUserInfo();
        // 获取任务中心 任务类型 5 且任务已开启未关闭
        List<TaskCenter> taskCenters =
            taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery().eq(TaskCenter::getTaskTypeId, 5)
                .eq(TaskCenter::getTaskState, 1).le(TaskCenter::getOpeningTime, new Date()));
        // 获取个人任务 且已完成 任务 且未领取sp
        List<Integer> taskCenterIds =
            taskCenters.stream().map(TaskCenter::getId).collect(Collectors.toList());
        if (taskCenterIds.size() > 0) {
            List<TaskCenterUser> taskCenterUsers = taskCenterUserMapper.selectList(
                Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .in(TaskCenterUser::getTaskCenterId, taskCenterIds).eq(TaskCenterUser::getIsReceive, 0)
                    .eq(TaskCenterUser::getIsComplete, 1));
            if (CollectionUtil.isNotEmpty(taskCenterUsers)) {
                List<Integer> taskCenterUserIds =
                    taskCenterUsers.stream().map(TaskCenterUser::getId).collect(Collectors.toList());
                // 修改领取任务状态
                taskCenterUserMapper.update(null,
                    Wrappers.<TaskCenterUser>lambdaUpdate().in(TaskCenterUser::getId, taskCenterUserIds)
                        .set(TaskCenterUser::getReward, taskCenters.get(0).getReward())
                        .set(TaskCenterUser::getAddress, address).set(TaskCenterUser::getIsReceive, 1));
            }
        }
        return R.ok();
    }

    /**
     * 统计总数SP
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @Override
    public R getStatisticalSPService() {
        // 获取用户信息
        Account userInfo = accountService.getUserInfo();

        BigDecimal sp = taskCenterUserMapper.getUserSPsum(userInfo.getUuid());

        return R.okData(sp);
    }

    /**
     * 浏览社区热门动态30s
     *
     * @param id -动态id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @Override
    public R getDynamic30SService() {
        // 获取用户信息
        Account userInfo = accountService.getUserInfo();
        ReentrantLock userLock = userLocks.computeIfAbsent(userInfo.getUuid(), k -> new ReentrantLock());
        userLock.lock();
        try {
            // 查询 浏览社区热门动态30s 这个任务id
            TaskCenter taskCenter = taskCenterMapper.selectOne(Wrappers.<TaskCenter>lambdaQuery()
                .eq(TaskCenter::getTaskState, 1).eq(TaskCenter::getTaskTypeId, 1)
                .eq(TaskCenter::getIntroduce, SQRM30)
                .le(TaskCenter::getOpeningTime, DateUtil.formatDateTime(DateUtil.beginOfDay(new Date())))
                .ge(TaskCenter::getCompletionTime, DateUtil.formatDateTime(DateUtil.endOfDay(new Date()))));
            if (taskCenter != null) {
                // 查询 在任务完成表中是否有完成记录
                TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                    .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                // 说明没有完成记录则插入
                if (taskCenterUser == null) {
                    TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                    taskCenterUserNew.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setReward(taskCenter.getReward()).setIsComplete(1).setCompletionTime(new Date())
                        .setCompletionCount(1);
                    taskCenterUserMapper.insert(taskCenterUserNew);
                } else {
                    return R.okData(true);
                }
            }
        } finally {
            userLock.unlock();
        }
        return R.okData(true);
    }

    /**
     * 浏览最新动态30s
     *
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @Override
    public R getNewDynamic30SService() {
        // 获取用户信息
        Account userInfo = accountService.getUserInfo();
        ReentrantLock userLock = userLocks.computeIfAbsent(userInfo.getUuid(), k -> new ReentrantLock());
        userLock.lock();
        try {
            // 查询 浏览社区热门动态30s 这个任务id
            TaskCenter taskCenter = taskCenterMapper.selectOne(Wrappers.<TaskCenter>lambdaQuery()
                .eq(TaskCenter::getTaskState, 1).eq(TaskCenter::getTaskTypeId, 1)
                .eq(TaskCenter::getDescribe, SQZX30)
                .le(TaskCenter::getOpeningTime, DateUtil.formatDateTime(DateUtil.beginOfDay(new Date())))
                .ge(TaskCenter::getCompletionTime, DateUtil.formatDateTime(DateUtil.endOfDay(new Date()))));
            if (taskCenter != null) {
                // 查询 在任务完成表中是否有完成记录
                TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                    .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                // 说明没有完成记录则插入
                if (taskCenterUser == null) {
                    TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                    taskCenterUserNew.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setReward(taskCenter.getReward()).setIsComplete(1).setCompletionTime(new Date())
                        .setCompletionCount(1);
                    taskCenterUserMapper.insert(taskCenterUserNew);
                } else {
                    return R.okData(true);
                }
            }
        } finally {
            userLock.unlock();
        }

        return R.okData(true);
    }

    /**
     * 奖励明细列表
     * 
     * @param page
     * @param pageSize
     * @param type
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/03
     */
    @Override
    public R getListSpDetail(Integer page, Integer pageSize, Integer type) {
        Account userInfo = accountService.getUserInfo();
        List<SpRecordVo> spRecordVos = new ArrayList<>();
        Integer totalCount = 0;
        // 查询全部
        if (type == 0) {
            // 耗时统计
            long startTime = System.currentTimeMillis();
            // totalCount = taskCenterUserMapper.selectSpCount(userInfo.getUuid());
            // log.error("查询全部耗时：" + (System.currentTimeMillis() - startTime) + "ms");
            // int start = (page - 1) * pageSize;
            // if (start < totalCount) {
            // spRecordVos = taskCenterUserMapper.selectSpList(start, pageSize, userInfo.getUuid());
            // }
            Page<SpRecordVo> result =
                taskCenterUserMapper.selectSpListPage(new Page(page, pageSize), userInfo.getUuid());
            spRecordVos = result.getRecords();
            // log.error("查询全部耗时：" + (System.currentTimeMillis() - startTime) + "ms");
            // 查询签到
        } else if (type == 1) {
            totalCount = taskCenterUserMapper.selectSiginSpCount(userInfo.getUuid());
            int start = (page - 1) * pageSize;
            if (start < totalCount) {
                spRecordVos = taskCenterUserMapper.selectSiginSpList(start, pageSize, userInfo.getUuid());
            }
        }
        // 查询 任务
        else if (type == 2) {
            totalCount = taskCenterUserMapper.selectTaskSpCount(userInfo.getUuid());
            int start = (page - 1) * pageSize;
            if (start < totalCount) {
                spRecordVos = taskCenterUserMapper.selectTaskSpList(start, pageSize, userInfo.getUuid());
            }
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, spRecordVos);
        return R.okData(pageUtils);
    }

    @Override
    public R getListSpDetailRecord(String hash) {
        Chain chain = chainMapper.selectOne(Wrappers.<Chain>lambdaQuery().eq(Chain::getChainId, chainId));
        BigInteger block = new BigInteger("0");
        String to = "";
        String fe = "";
        BigDecimal gasUsed = new BigDecimal("0");
        try {
            TransactionReceipt result = BNBUtil.ethGetTransactionReceipt(hash).getResult();
            if (result != null) {
                block = result.getBlockNumber();
                to = result.getTo();
                gasUsed = (new BigDecimal(result.getGasUsed())).divide(new BigDecimal(BigInteger.TEN.pow(9)));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        RechargeMessageVo rechargeMessageVo = new RechargeMessageVo();
        rechargeMessageVo.setChainLogo(chain.getChainLogo()).setChainName(chain.getChainName())
            .setReceiveAddress(to).setFee(String.valueOf(gasUsed)).setHash(hash).setBlock(block);
        return R.okData(rechargeMessageVo);
    }

    /**
     * 获取任务 中心签到和任务统计
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @Override
    public R getTaskCenterStatisticsService() {
        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        // 获取总共签到天数
        Integer signCount = signInHistMapper.selectCount(
            Wrappers.<SignInHist>lambdaQuery().eq(SignInHist::getAccountUuid, userInfo.getUuid()));
        // 累计获得所有SP
        BigDecimal totalSp = taskCenterUserMapper.getTotalSp(userInfo.getUuid());
        result.put("signInDays", signCount);
        result.put("totalSp", totalSp);
        return R.okData(result);
    }

    /**
     * 获取任务中心任务变动推送
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @Override
    public R getTaskChangesService() {
        String globalConfig = globalConfigService.getGlobalConfig(TASK_CHANGE_NOTIFICATION);
        return R.okData(globalConfig);
    }

    /**
     * 任务领取状态
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    @Override
    public R isReceiveStateService() {
        boolean result = false;
        Account userInfo = accountService.getUserInfo();
        Integer registrationSource = userInfo.getRegistrationSource();
        if (ObjectUtil.equals(3, registrationSource)) {
            return R.okData(result);
        }
        // 查询日常任务--周期任务--限时任务 是否有已完成 未领取的
        List<TaskCenter> taskCenters = taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery()
            .eq(TaskCenter::getTaskState, 1).in(TaskCenter::getTaskTypeId, Arrays.asList(1, 2, 4)));
        if (CollectionUtil.isNotEmpty(taskCenters)) {
            List<Integer> taskCenterIds =
                taskCenters.stream().map(TaskCenter::getId).collect(Collectors.toList());
            List<TaskCenterUser> taskCenterUserList = taskCenterUserMapper.selectList(
                Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .eq(TaskCenterUser::getIsReceive, 0).eq(TaskCenterUser::getIsComplete, 1)
                    .in(TaskCenterUser::getTaskCenterId, taskCenterIds));
            if (CollectionUtil.isNotEmpty(taskCenterUserList)) {
                result = true;
            }
        }
        return R.okData(result);
    }

    /**
     * 直播间红点数据
     *
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/13
     */
    @Override
    public R numberOfRedDotsService(HttpServletRequest request) {
        String channel = request.getHeader("channel");
        if (StringUtils.isEmpty(channel)) {
            channel = "JIEWAI";
        }
        Account userInfo = accountService.getUserInfo();
        // 刷新任务统计
        statisticalTasksService();
        Map<String, Object> result = new HashMap<>();
        int count = 0;
        // 统计签到任务
        SignIn signIn = signInMapper
            .selectOne(Wrappers.<SignIn>lambdaQuery().eq(SignIn::getAccountUuid, userInfo.getUuid())
                .eq(SignIn::getSignInDate, DateUtil.beginOfDay(DateUtil.date())));
        if (signIn == null) {
            count = count + 1;
        }
        // 统计任务中心任务
        List<TaskCenter> taskCenterList =
            taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery().eq(TaskCenter::getTaskState, 1));
        // 更具特定的版本返回指定任务
        // 获取要移除的任务
        List<ModuleSwitch> moduleSwitches =
            moduleSwitchMapper.selectList(Wrappers.<ModuleSwitch>lambdaQuery().eq(ModuleSwitch::getTypeId, 1)
                .eq(ModuleSwitch::getChannel, channel).eq(ModuleSwitch::getValue, false));
        if (CollectionUtil.isNotEmpty(moduleSwitches)) {
            // 过滤掉taskCenters中Describe和moduleSwitches中key相同的任务
            taskCenterList.removeIf(taskCenter -> {
                return moduleSwitches.stream()
                    .anyMatch(moduleSwitch -> moduleSwitch.getKey().equals(taskCenter.getDescribe()));
            });
        }
        // 统计用户完成任务未领取
        List<TaskCenterUser> taskCenterUserList = taskCenterUserMapper.selectList(
            Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                .eq(TaskCenterUser::getIsComplete, 1).eq(TaskCenterUser::getIsReceive, 0));
        // 更具任务id去重
        // 这里需要对可以重复完成的任务去重，更具任务id
        List<TaskCenterUser> taskCenterUserList1 = taskCenterUserList.stream()
            .collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getTaskCenterId()))),
                ArrayList::new));
        taskCenterUserList = taskCenterUserList.stream().distinct().collect(Collectors.toList());
        // 遍历循环匹配对应的任务id 然后增加count数量
        for (TaskCenter taskCenter : taskCenterList) {
            for (TaskCenterUser taskCenterUser : taskCenterUserList1) {
                if (taskCenter.getId().equals(taskCenterUser.getTaskCenterId())) {
                    count = count + 1;
                }
            }
        }
        result.put("taskCenter", count);
        return R.okData(result);
    }

    /**
     * 列表红点任务展示
     *
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/18
     */
    @Override
    public R listRedDotsCountService(HttpServletRequest request) {
        String channel = request.getHeader("channel");
        if (StringUtils.isEmpty(channel)) {
            channel = "JIEWAI";
        }
        Account userInfo = accountService.getUserInfo();
        Map<String, Object> result = new HashMap<>();
        // 1-日常任务2-周期任务3-新手任务4-限时任务5-长期任务
        int count1 = 0;
        int count2 = 0;
        int count3 = 0;
        int count4 = 0;
        int count5 = 0;
        // 统计任务中心任务
        List<TaskCenter> taskCenterList =
            taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery().eq(TaskCenter::getTaskState, 1));
        // 更具特定的版本返回指定任务
        // 获取要移除的任务
        List<ModuleSwitch> moduleSwitches =
            moduleSwitchMapper.selectList(Wrappers.<ModuleSwitch>lambdaQuery().eq(ModuleSwitch::getTypeId, 1)
                .eq(ModuleSwitch::getChannel, channel).eq(ModuleSwitch::getValue, false));
        if (CollectionUtil.isNotEmpty(moduleSwitches)) {
            // 过滤掉taskCenters中Describe和moduleSwitches中key相同的任务
            taskCenterList.removeIf(taskCenter -> {
                return moduleSwitches.stream()
                    .anyMatch(moduleSwitch -> moduleSwitch.getKey().equals(taskCenter.getDescribe()));
            });
        }
        // 统计用户完成任务未领取
        List<TaskCenterUser> taskCenterUserList = taskCenterUserMapper.selectList(
            Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                .eq(TaskCenterUser::getIsComplete, 1).eq(TaskCenterUser::getIsReceive, 0));
        // 这里需要对可以重复完成的任务去重，更具任务id
        List<TaskCenterUser> taskCenterUserList1 = taskCenterUserList.stream()
            .collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getTaskCenterId()))),
                ArrayList::new));
        // 遍历循环匹配对应的任务id 然后增加count数量
        for (TaskCenter taskCenter : taskCenterList) {
            for (TaskCenterUser taskCenterUser : taskCenterUserList1) {
                if (taskCenter.getId().equals(taskCenterUser.getTaskCenterId())) {
                    // 在更具任务类型区分
                    if (taskCenter.getTaskTypeId() == 1) {
                        count1 = count1 + 1;
                    } else if (taskCenter.getTaskTypeId() == 2) {
                        count2 = count2 + 1;
                    } else if (taskCenter.getTaskTypeId() == 3) {
                        count3 = count3 + 1;
                    } else if (taskCenter.getTaskTypeId() == 4) {
                        count4 = count4 + 1;
                    } else if (taskCenter.getTaskTypeId() == 5) {
                        count5 = count5 + 1;
                    }

                }
            }
        }
        result.put("taskCenter1", count1);
        result.put("taskCenter2", count2);
        result.put("taskCenter3", count3);
        result.put("taskCenter4", count4);
        result.put("taskCenter5", count5);
        return R.okData(result);
    }
    // ========工具方法===================================

    /**
     * @param signInVos
     * @param signInRewardList
     * @param ylChainAccount
     * @param walletUserInfo
     * @param one
     */
    private void parameterAssembly1(List<SignInVo> signInVos, List<BigDecimal> signInRewardList) {
        for (int i = 0; i < signInRewardList.size(); i++) {
            SignInVo sign = new SignInVo();
            sign.setRewardMoney(signInRewardList.get(i));
            sign.setDays(i + 1);
            sign.setIsSign(false);
            sign.setCycleDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), i)));
            signInVos.add(sign);
        }
    }

    /**
     * 1-日常任务2-周期任务3-新手任务4-限时任务5-长期任务
     *
     * @param taskCenter
     * <AUTHOR>
     * @date 2024/05/29
     */
    private void executeTask(TaskCenter taskCenter, Account userInfo) {
//        log.info("开始执行任务: " + taskCenter.getTaskName() + "##" + Thread.currentThread().getName());
        Integer taskTypeId = taskCenter.getTaskTypeId();
        switch (taskTypeId) {
            // 1-日常任务
            case 1:
                dailyTasks(taskCenter, userInfo);
                break;
            // 2-周期任务
            case 2:
                periodicTasks(taskCenter, userInfo);
                break;
            // 3- 新手任务
            case 3:
                newbieTask(taskCenter, userInfo);
                break;
            // 4-限时任务
            case 4:
                dynamic50AndLike(taskCenter, userInfo);
                break;
            // 5-长期任务
            case 5:
                inviteUsers(taskCenter, userInfo);
                break;

        }
//        log.info("任务 " + taskCenter.getTaskName() + " 执行完成。");
    }

    /**
     * 日常任务 每天更新一次
     * 
     * @param taskCenter
     * @param userInfo
     * <AUTHOR>
     * @date 2024/05/31
     */
    public void dailyTasks(TaskCenter taskCenter, Account userInfo) {
        // 日常任务每天更新一次
        String introduce = taskCenter.getDescribe();

        // 任务已结束不进行统计=>任务结束时间
        Date completionTime = taskCenter.getCompletionTime();
        // 获取当前时间
        Date date = new Date();
        // 任务开始时间
        Date openingTime = taskCenter.getOpeningTime();
        // 当前时间要小于任务结束时间并且大于任务开始时间
        if (DateUtil.compare(date, completionTime) < 0 && DateUtil.compare(date, openingTime) > 0) {

            switch (introduce) {
                // 发布一条动态/评论/回复
                case FBYTDT:
                    TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    if (taskCenterUser == null) {
                        int flag = 0;// 任务完成标识 0-未完成 1-完成
                        String id = null;
                        // 查询看是否完成任务
                        // 查询广场是否发布一条动态 未删除 且 时间 大于等于任务开始时间
                        // 检查用户是否发表动态且动态是在任务开启之后发布的
                        if (flag == 0) {
                            Trends trends = trendsMapper.selectOne(
                                Wrappers.<Trends>lambdaQuery().eq(Trends::getAccountUuid, userInfo.getUuid())
                                    .eq(Trends::getRemoveFlag, 0).ge(Trends::getCreateTime, openingTime)
                                    .orderByDesc(Trends::getCreateTime).last("limit 1"));
                            flag = trends != null ? 1 : 0;
                            id = trends != null ? String.valueOf(trends.getId()) : null;

                        }
                        // 查询是否有回复 且回复时间 时间 大于等于任务开始时间
                        if (flag == 0) {
                            CommentReply commentReply =
                                commentReplyMapper.selectOne(Wrappers.<CommentReply>lambdaQuery()
                                    .eq(CommentReply::getAccountUuid, userInfo.getUuid())
                                    .eq(CommentReply::getRemoveFlag, 0)
                                    .ge(CommentReply::getCreateTime, openingTime)
                                    .orderByDesc(CommentReply::getCreateTime).last("limit 1"));
                            flag = commentReply != null ? 1 : 0;
                            id = commentReply != null ? String.valueOf(commentReply.getId()) : null;
                        }

                        // 查询是否有评论 且评论时间 大于等于任务开始时间
                        if (flag == 0) {
                            Comment comment = commentMapper.selectOne(Wrappers.<Comment>lambdaQuery()
                                .eq(Comment::getAccountUuid, userInfo.getUuid()).eq(Comment::getRemoveFlag, 0)
                                .ge(Comment::getCreateTime, openingTime).orderByDesc(Comment::getCreateTime)
                                .last("limit 1"));
                            flag = comment != null ? 1 : 0;
                            id = comment != null ? String.valueOf(comment.getId()) : null;
                        }

                        // 任务完成 插入记录
                        if (flag == 1) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId()).setTaskIdCompleted(id)
                                .setReward(taskCenter.getReward()).setIsComplete(1)
                                .setCompletionTime(new Date()).setCompletionCount(1);
                            // taskCenterUserMapper.insert(taskCenterUserNew);
                            insertTaskCenterUser(taskCenterUserNew, userInfo);
                        }
                    }
                    break;
                // 分享一条动态
                case FXYTDT:
                    TaskCenterUser taskCenterUser1 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    if (taskCenterUser1 == null) {
                        // 分享一条动态 时间大于等于任务开启时间
                        TrendsForward trendsForward =
                            trendsForwardMapper.selectOne(Wrappers.<TrendsForward>lambdaQuery()
                                .eq(TrendsForward::getAccountUuid, userInfo.getUuid())
                                .ge(TrendsForward::getCreateTime, openingTime)
                                .orderByDesc(TrendsForward::getCreateTime).last("limit 1"));
                        if (trendsForward != null) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId())
                                .setTaskIdCompleted(String.valueOf(trendsForward.getId()))
                                .setReward(taskCenter.getReward()).setIsComplete(1)
                                .setCompletionTime(new Date()).setCompletionCount(1);
                            // taskCenterUserMapper.insert(taskCenterUserNew);
                            insertTaskCenterUser(taskCenterUserNew, userInfo);
                        }
                    }
                    break;
                // 点赞一条动态
                case DZYTDT:
                    TaskCenterUser taskCenterUser2 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    if (taskCenterUser2 == null) {
                        // 点赞一条动态 未取消 时间大于等于任务开启时间
                        TrendsLikes trendsLikes = trendsLikesMapper.selectOne(Wrappers
                            .<TrendsLikes>lambdaQuery().eq(TrendsLikes::getAccountUuid, userInfo.getUuid())
                            .eq(TrendsLikes::getCancelFlag, 0).ge(TrendsLikes::getCreateTime, openingTime)
                            .orderByDesc(TrendsLikes::getCreateTime).last("limit 1"));
                        if (trendsLikes != null) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId())
                                .setTaskIdCompleted(String.valueOf(trendsLikes.getId()))
                                .setReward(taskCenter.getReward()).setIsComplete(1)
                                .setCompletionTime(new Date()).setCompletionCount(1);
                            // taskCenterUserMapper.insert(taskCenterUserNew);
                            insertTaskCenterUser(taskCenterUserNew, userInfo);
                        }
                    }
                    break;
                // 发布视屏动态
                case FBSPDT:
                    TaskCenterUser taskCenterUser3 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    if (taskCenterUser3 == null) {
                        // 发布视屏动态 未取消 时间大于等于任务开启时间
                        Trends trendsSP = trendsMapper.selectOne(Wrappers.<Trends>lambdaQuery()
                            .eq(Trends::getAccountUuid, userInfo.getUuid()).eq(Trends::getType, 5)
                            .eq(Trends::getRemoveFlag, 0).ge(Trends::getCreateTime, openingTime)
                            .orderByDesc(Trends::getCreateTime).last("limit 1"));
                        if (trendsSP != null) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId())
                                .setTaskIdCompleted(String.valueOf(trendsSP.getId()))
                                .setReward(taskCenter.getReward()).setIsComplete(1)
                                .setCompletionTime(new Date()).setCompletionCount(1);
                            // taskCenterUserMapper.insert(taskCenterUserNew);
                            insertTaskCenterUser(taskCenterUserNew, userInfo);
                        }
                    }
                    break;
                // 充值中心完成任一购买
                case CZZXWCRYGM:
                    TaskCenterUser taskCenterUser4 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    if (taskCenterUser4 == null) {
                        // 充值中心已到账 支付时间大于等于任务开启时间
                        LifeRechargeRecord lifeRechargeRecord = lifeRechargeRecordMapper.selectOne(Wrappers
                            .<LifeRechargeRecord>lambdaQuery()
                            .eq(LifeRechargeRecord::getAccountUuid, userInfo.getUuid())
                            .eq(LifeRechargeRecord::getStatus, 1).eq(LifeRechargeRecord::getRefundState, 0)
                            .ge(LifeRechargeRecord::getPayTime, openingTime)
                            .orderByDesc(LifeRechargeRecord::getPayTime).last("limit 1"));
                        if (lifeRechargeRecord != null) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId())
                                .setTaskIdCompleted(String.valueOf(lifeRechargeRecord.getId()))
                                .setReward(taskCenter.getReward()).setIsComplete(1)
                                .setCompletionTime(new Date()).setCompletionCount(1);
                            // taskCenterUserMapper.insert(taskCenterUserNew);
                            insertTaskCenterUser(taskCenterUserNew, userInfo);
                        }
                    }
                    break;

                // 购买域名
                case GMYM:
                    TaskCenterUser taskCenterUser5 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    if (taskCenterUser5 == null) {
                        // 购买域名成功 创建时间大于等于任务开启时间
                        DomainAccount domainAccount =
                            domainAccountMapper.selectOne(Wrappers.<DomainAccount>lambdaQuery()
                                .eq(DomainAccount::getAccountUuid, userInfo.getUuid())
                                .ge(DomainAccount::getCreateTime, openingTime)
                                .orderByDesc(DomainAccount::getCreateTime).last("limit 1"));
                        if (domainAccount != null) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId())
                                .setTaskIdCompleted(String.valueOf(domainAccount.getId()))
                                .setReward(taskCenter.getReward()).setIsComplete(1)
                                .setCompletionTime(new Date()).setCompletionCount(1);
                            // taskCenterUserMapper.insert(taskCenterUserNew);
                            insertTaskCenterUser(taskCenterUserNew, userInfo);
                        }
                    }
                    break;

                // 预约挂号
                case YYGH:
                    TaskCenterUser taskCenterUser6 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    if (taskCenterUser6 == null) {

                        // 获取已支付未退款的订单
                        List<MixOrder> mixOrders = mixOrderMapper.selectList(
                            Wrappers.<MixOrder>lambdaQuery().eq(MixOrder::getAccountUuid, userInfo.getUuid())
                                .eq(MixOrder::getApplicatonId, 14).eq(MixOrder::getPayStatus, 1)
                                .eq(MixOrder::getRefundState, 0)

                        );
                        if (CollectionUtil.isNotEmpty(mixOrders)) {
                            String result = null;

                            // 获取orderNumber集合
                            List<String> orderNumberList =
                                mixOrders.stream().map(MixOrder::getOrderNumber).collect(Collectors.toList());
                            // 批量获取订单信息
                            List<MixOrderView> orderInfos =
                                mixOrderViewMapper.selectList(Wrappers.<MixOrderView>lambdaQuery()
                                    .in(MixOrderView::getOrderNumber, orderNumberList));
                            for (MixOrderView mixOrderView : orderInfos) {
                                String orderDetailJson = mixOrderView.getOrderDetailJson();
                                if (StrUtil.isNotBlank(orderDetailJson)) {
                                    // 判断就诊时间要小于当前时间 且 就诊时间要大于任务开始时间
                                    String visitPeriod = MedicalOrderParser.getVisitPeriod(orderDetailJson);
                                    System.out.println(visitPeriod);
                                    String[] split = visitPeriod.split("/");
                                    String s = split[0];
                                    // 就诊时间
                                    DateTime parse =
                                        DateUtil.parse(s, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                                    if (parse.isAfter(openingTime)) {
                                        if (parse.isBefore(new Date())) {
                                            result = mixOrderView.getOrderNumber();
                                            break;
                                        }
                                    }
                                }
                            }
                            if (StrUtil.isNotBlank(result)) {
                                TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                                taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                    .setTaskCenterId(taskCenter.getId()).setTaskIdCompleted(result)
                                    .setReward(taskCenter.getReward()).setIsComplete(1)
                                    .setCompletionTime(new Date()).setCompletionCount(1);
                                // taskCenterUserMapper.insert(taskCenterUserNew);
                                insertTaskCenterUser(taskCenterUserNew, userInfo);
                            }
                        }
                    }
                    break;
                // 观影任务
                // 可重复完成/当日有效
                // 票数*0.5SP
                case GYRW:
                    List<TaskCenterUser> taskCenterUser7 = taskCenterUserMapper.selectList(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    if (CollectionUtil.isEmpty(taskCenterUser7)) {
                        // 电影出票时间大于任务开启时间 电影状态是已结算
                        List<MovieOrder> movieOrders = movieOrderMapper.selectList(Wrappers
                            .<MovieOrder>lambdaQuery().eq(MovieOrder::getAccountUuid, userInfo.getUuid())
                            .eq(MovieOrder::getOrderStatus, 5).ge(MovieOrder::getTicketTime, openingTime));
                        if (CollectionUtil.isNotEmpty(movieOrders)) {
                            List<TaskCenterUser> taskCenterUserNewList = new ArrayList<>();
                            for (MovieOrder movieOrder : movieOrders) {
                                BigDecimal reward = taskCenter.getReward();
                                // 统计总座位总数
                                Integer seatCount = movieOrder.getOrderNum();
                                // 计算总获得的SP
                                BigDecimal spAmount = reward.multiply(new BigDecimal(seatCount));
                                TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                                taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                    .setTaskCenterId(taskCenter.getId())
                                    .setTaskIdCompleted(String.valueOf(movieOrder.getId()))
                                    .setReward(spAmount).setIsComplete(1).setCompletionTime(new Date())
                                    .setCompletionCount(seatCount);
                                taskCenterUserNewList.add(taskCenterUserNew);
                            }
                            taskCenterUserService.saveBatch(taskCenterUserNewList);
                        }
                    } else {
                        // 已有完成的任务 获取任务完成的id
                        List<Integer> completedTaskIds =
                            taskCenterUser7.stream().map(TaskCenterUser::getTaskIdCompleted)
                                .map(Integer::parseInt).collect(Collectors.toList());

                        // 电影出票时间大于任务开启时间 电影状态是已结算 并排除已经完成的任务
                        List<MovieOrder> movieOrders = movieOrderMapper.selectList(Wrappers
                            .<MovieOrder>lambdaQuery().eq(MovieOrder::getAccountUuid, userInfo.getUuid())
                            // 排除已经完成任务的Id
                            .notIn(!completedTaskIds.isEmpty(), MovieOrder::getId, completedTaskIds)
                            .eq(MovieOrder::getOrderStatus, 5).ge(MovieOrder::getTicketTime, openingTime));
                        if (CollectionUtil.isNotEmpty(movieOrders)) {
                            List<TaskCenterUser> taskCenterUserNewList = new ArrayList<>();
                            for (MovieOrder movieOrder : movieOrders) {
                                BigDecimal reward = taskCenter.getReward();
                                // 统计总座位总数
                                Integer seatCount = movieOrder.getOrderNum();
                                // 计算总获得的SP
                                BigDecimal spAmount = reward.multiply(new BigDecimal(seatCount));
                                TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                                taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                    .setTaskCenterId(taskCenter.getId())
                                    .setTaskIdCompleted(String.valueOf(movieOrder.getId()))
                                    .setReward(spAmount).setIsComplete(1).setCompletionTime(new Date())
                                    .setCompletionCount(seatCount);
                                taskCenterUserNewList.add(taskCenterUserNew);
                            }
                            taskCenterUserService.saveBatch(taskCenterUserNewList);
                        }
                    }
                    break;
            }
        }
    }

    /**
     * 周期任务 每周更新一次
     * 
     * @param taskCenter
     * @param userInfo
     * <AUTHOR>
     * @date 2024/06/04
     */
    public void periodicTasks(TaskCenter taskCenter, Account userInfo) {
        // 期任务 每周更新一次
        String introduce = taskCenter.getDescribe();
        // 任务完成数量
        Integer completionCount = taskCenter.getCompletionCount();

        // 任务已结束不进行统计=>任务结束时间
        Date completionTime = taskCenter.getCompletionTime();
        // 获取当前时间
        Date date = new Date();
        // 任务开始时间
        Date openingTime = taskCenter.getOpeningTime();
        // 当前时间要小于任务结束时间并且大于任务开始时间
        if (DateUtil.compare(date, completionTime) < 0 && DateUtil.compare(date, openingTime) > 0) {
            switch (introduce) {
                // 累计发布10条动态
                case LJFB10DT:
                    TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 未开始且未完成
                    if (taskCenterUser == null) {
                        // 查询看是否完成任务
                        // 查询广场是否发布一条动态 未删除 且 时间 大于等于任务开始时间
                        // 检查用户是否发表动态且动态是在任务开启之后发布的
                        List<Trends> trends = trendsMapper.selectList(Wrappers.<Trends>lambdaQuery()
                            .eq(Trends::getAccountUuid, userInfo.getUuid()).eq(Trends::getRemoveFlag, 0)
                            .ge(Trends::getCreateTime, openingTime).orderByDesc(Trends::getCreateTime));
                        int size = trends.size();
                        int flag = size - completionCount >= 0 ? 1 : 0;
                        // 任务开始
                        if (size != 0) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId())
                                .setTaskIdCompleted(String.valueOf(trends.get(0).getId()))
                                .setReward(taskCenter.getReward()).setIsComplete(flag)
                                .setCompletionTime(new Date())
                                .setCompletionCount(size - completionCount >= 0 ? completionCount : size);
                            taskCenterUserMapper.insert(taskCenterUserNew);
                        }
                        // 任务开始
                    } else {
                        Integer isComplete = taskCenterUser.getIsComplete();
                        // 数据库任务完成数量
                        Integer completionCount1 = taskCenterUser.getCompletionCount();
                        // 判断任务是否完成
                        // 统计更新
                        List<Trends> trends = trendsMapper.selectList(Wrappers.<Trends>lambdaQuery()
                            .eq(Trends::getAccountUuid, userInfo.getUuid()).eq(Trends::getRemoveFlag, 0)
                            .ge(Trends::getCreateTime, openingTime).orderByDesc(Trends::getCreateTime));
                        int size = trends.size();
                        // 需要大于数据库完成任务数才更新
                        int flag = size - completionCount >= 0 ? 1 : 0;
                        taskCenterUserMapper.update(null,
                            Wrappers.<TaskCenterUser>lambdaUpdate()
                                .eq(TaskCenterUser::getId, taskCenterUser.getId())
                                .set(TaskCenterUser::getIsComplete, flag)
                                .set(TaskCenterUser::getCompletionTime, new Date())
                                .set(TaskCenterUser::getCompletionCount,
                                    size - completionCount >= 0 ? completionCount : size));

                    }
                    break;
                // 累计获得30个赞
                case LJHD30GDZ:
                    TaskCenterUser taskCenterUser30 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 未开始且未完成 累计获得30个赞
                    if (taskCenterUser30 == null) {
                        // 查询看是否完成任务
                        List<Trends> trends = trendsMapper.selectList(
                            Wrappers.<Trends>lambdaQuery().eq(Trends::getAccountUuid, userInfo.getUuid())
                                .eq(Trends::getRemoveFlag, 0).orderByDesc(Trends::getCreateTime));
                        List<Integer> trendIds =
                            trends.stream().map(Trends::getId).collect(Collectors.toList());
                        if (trendIds.size() > 0) {
                            List<TrendsLikes> trendsLikes = trendsLikesMapper.selectList(Wrappers
                                .<TrendsLikes>lambdaQuery().in(TrendsLikes::getTrendsId, trendIds)
                                .eq(TrendsLikes::getCancelFlag, 0).ge(TrendsLikes::getCreateTime, openingTime)

                            );
                            // 累计获得30个赞
                            int likeNums = trendsLikes.size();

                            int flag = likeNums - completionCount >= 0 ? 1 : 0;
                            // 任务开始
                            if (likeNums != 0) {
                                TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                                taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                    .setTaskCenterId(taskCenter.getId())
                                    .setTaskIdCompleted(String.valueOf(trends.get(0).getId()))
                                    .setReward(taskCenter.getReward()).setIsComplete(flag)
                                    .setCompletionTime(new Date()).setCompletionCount(
                                        likeNums - completionCount >= 0 ? completionCount : likeNums);
                                // taskCenterUserMapper.insert(taskCenterUserNew);
                                insertTaskCenterUser(taskCenterUserNew, userInfo);
                            }
                        }
                        // 任务开始
                    } else {
                        Integer isComplete = taskCenterUser30.getIsComplete();
                        // 数据库任务完成数量
                        Integer completionCount1 = taskCenterUser30.getCompletionCount();
                        // 判断任务是否完成
                        // 统计更新
                        // 查询看是否完成任务
                        List<Trends> trends = trendsMapper.selectList(
                            Wrappers.<Trends>lambdaQuery().eq(Trends::getAccountUuid, userInfo.getUuid())
                                .eq(Trends::getRemoveFlag, 0).orderByDesc(Trends::getCreateTime));
                        List<Integer> trendIds =
                            trends.stream().map(Trends::getId).collect(Collectors.toList());
                        if (trendIds.size() > 0) {
                            List<TrendsLikes> trendsLikes = trendsLikesMapper.selectList(Wrappers
                                .<TrendsLikes>lambdaQuery().in(TrendsLikes::getTrendsId, trendIds)
                                .eq(TrendsLikes::getCancelFlag, 0).ge(TrendsLikes::getCreateTime, openingTime)

                            );
                            // 累计获得30个赞
                            int likeNums = trendsLikes.size();
                            int flag = likeNums - completionCount >= 0 ? 1 : 0;
                            taskCenterUserMapper.update(null,
                                Wrappers.<TaskCenterUser>lambdaUpdate()
                                    .eq(TaskCenterUser::getId, taskCenterUser30.getId())
                                    .set(TaskCenterUser::getIsComplete, flag)
                                    .set(TaskCenterUser::getCompletionTime, new Date())
                                    .set(TaskCenterUser::getCompletionCount,
                                        likeNums - completionCount >= 0 ? completionCount : likeNums));

                        }
                    }
                    break;
                // 新增30个粉丝
                case XZ30GFS:
                    TaskCenterUser taskCenterUser3 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 未开始且未完成 新增30个粉丝
                    if (taskCenterUser3 == null) {
                        // 查询看是否完成任务
                        List<Follow> follows = followMapper.selectList(Wrappers.<Follow>lambdaQuery()
                            .eq(Follow::getAccountUuid, userInfo.getUuid()).eq(Follow::getRemoveFlag, 0)
                            .ge(Follow::getCreateTime, openingTime).orderByDesc(Follow::getCreateTime));
                        List<Follow> followsOld = followMapper.selectList(
                            Wrappers.<Follow>lambdaQuery().eq(Follow::getAccountUuid, userInfo.getUuid())
                                .lt(Follow::getCreateTime, openingTime).orderByDesc(Follow::getCreateTime));
                        List<Follow> followsOldSet = followsOld.stream()
                            .collect(Collectors.toMap(Follow::getFollowUuid, Function.identity(),
                                (oldValue, newValue) -> oldValue))
                            .values().stream().collect(Collectors.toList());

                        List<Follow> followList = new ArrayList<>();

                        for (Follow follow : follows) {
                            boolean flag = true;
                            String folowUuid = follow.getFollowUuid();
                            for (Follow follow1 : followsOldSet) {
                                String folowUuid1 = follow1.getFollowUuid();
                                if (folowUuid1.equals(folowUuid)) {
                                    flag = false;
                                    break;
                                }
                            }
                            if (flag) {
                                followList.add(follow);
                            }
                        }
                        // 新增30个粉丝
                        int size = followList.size();
                        int flag = size - completionCount >= 0 ? 1 : 0;
                        // 任务开始
                        if (size != 0) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId())
                                .setTaskIdCompleted(String.valueOf(followList.get(0).getId()))
                                .setReward(taskCenter.getReward()).setIsComplete(flag)
                                .setCompletionTime(new Date())
                                .setCompletionCount(size - completionCount >= 0 ? completionCount : size);
                            // taskCenterUserMapper.insert(taskCenterUserNew);
                            insertTaskCenterUser(taskCenterUserNew, userInfo);
                        }
                        // 任务开始
                    } else {
                        Integer isComplete = taskCenterUser3.getIsComplete();
                        // 数据库任务完成数量
                        Integer completionCount1 = taskCenterUser3.getCompletionCount();
                        // 查询看是否完成任务
                        List<Follow> follows = followMapper.selectList(Wrappers.<Follow>lambdaQuery()
                            .eq(Follow::getAccountUuid, userInfo.getUuid()).eq(Follow::getRemoveFlag, 0)
                            .ge(Follow::getCreateTime, openingTime).orderByDesc(Follow::getCreateTime));
                        List<Follow> followsOld = followMapper.selectList(
                            Wrappers.<Follow>lambdaQuery().eq(Follow::getAccountUuid, userInfo.getUuid())
                                .lt(Follow::getCreateTime, openingTime).orderByDesc(Follow::getCreateTime));
                        // 去重
                        List<Follow> followsOldSet = followsOld.stream()
                            .collect(Collectors.toMap(Follow::getFollowUuid, Function.identity(),
                                (oldValue, newValue) -> oldValue))
                            .values().stream().collect(Collectors.toList());
                        List<Follow> followList = new ArrayList<>();

                        for (Follow follow : follows) {
                            boolean flag = true;
                            String folowUuid = follow.getFollowUuid();
                            for (Follow follow1 : followsOldSet) {
                                String folowUuid1 = follow1.getFollowUuid();
                                if (folowUuid1.equals(folowUuid)) {
                                    flag = false;
                                    break;
                                }
                            }
                            if (flag) {
                                followList.add(follow);
                            }
                        }
                        // 新增30个粉丝
                        int size = followList.size();
                        int flag = size - completionCount >= 0 ? 1 : 0;
                        taskCenterUserMapper.update(null,
                            Wrappers.<TaskCenterUser>lambdaUpdate()
                                .eq(TaskCenterUser::getId, taskCenterUser3.getId())
                                .set(TaskCenterUser::getIsComplete, flag)
                                .set(TaskCenterUser::getCompletionTime, new Date())
                                .set(TaskCenterUser::getCompletionCount,
                                    size - completionCount >= 0 ? completionCount : size));

                    }
                    break;
                // 成功交易四次域名
                case CGJY4CYM:
                    TaskCenterUser taskCenterUser4 = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 未开始且未完成 成功交易四次域名
                    if (taskCenterUser4 == null) {
                        // 查询看是否完成任务
                        List<DomainTransferRecord> domainTransferRecords =
                            domainTransferRecordMapper.selectList(Wrappers.<DomainTransferRecord>lambdaQuery()
                                .and(dr -> dr.eq(DomainTransferRecord::getSellerUuid, userInfo.getUuid()).or()
                                    .eq(DomainTransferRecord::getBuyerUuid, userInfo.getUuid()))
                                .ge(DomainTransferRecord::getCreateTime, openingTime)
                                .orderByDesc(DomainTransferRecord::getCreateTime));
                        // 3条动态获得热门
                        int size = domainTransferRecords.size();
                        int flag = size - completionCount >= 0 ? 1 : 0;
                        // 任务开始
                        if (size != 0) {
                            TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                            taskCenterUserNew.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId())
                                .setTaskIdCompleted(String.valueOf(domainTransferRecords.get(0).getId()))
                                .setReward(taskCenter.getReward()).setIsComplete(flag)
                                .setCompletionTime(new Date())
                                .setCompletionCount(size - completionCount >= 0 ? completionCount : size);
                            // taskCenterUserMapper.insert(taskCenterUserNew);
                            insertTaskCenterUser(taskCenterUserNew, userInfo);
                        }
                        // 任务开始
                    } else {
                        Integer isComplete = taskCenterUser4.getIsComplete();
                        // 数据库任务完成数量
                        Integer completionCount1 = taskCenterUser4.getCompletionCount();
                        // 判断任务是否完成
                        // 统计更新
                        List<DomainTransferRecord> domainTransferRecords =
                            domainTransferRecordMapper.selectList(Wrappers.<DomainTransferRecord>lambdaQuery()
                                .and(dr -> dr.eq(DomainTransferRecord::getSellerUuid, userInfo.getUuid()).or()
                                    .eq(DomainTransferRecord::getBuyerUuid, userInfo.getUuid()))
                                .ge(DomainTransferRecord::getCreateTime, openingTime)
                                .orderByDesc(DomainTransferRecord::getCreateTime));
                        // 3条动态获得热门
                        int size = domainTransferRecords.size();
                        // 需要大于数据库完成任务数才更新
                        int flag = size - completionCount >= 0 ? 1 : 0;
                        taskCenterUserMapper.update(null,
                            Wrappers.<TaskCenterUser>lambdaUpdate()
                                .eq(TaskCenterUser::getId, taskCenterUser4.getId())
                                .set(TaskCenterUser::getIsComplete, flag)
                                .set(TaskCenterUser::getCompletionTime, new Date())
                                .set(TaskCenterUser::getCompletionCount,
                                    size - completionCount >= 0 ? completionCount : size));

                    }
                    break;
            }
        }
    }

    /**
     * 新手任务有且仅限制获得一次奖励
     * 
     * @param taskCenter
     * @param userInfo
     * <AUTHOR>
     * @date 2024/05/31
     */
    public void newbieTask(TaskCenter taskCenter, Account userInfo) {
        // 新手任务只有一次 需要做缓存处理 用户uuid 加任务名称 key
        String introduce = taskCenter.getDescribe();
        String key = introduce + ":" + userInfo.getUuid();
        switch (introduce) {
            // 设置域名名称
            case SZYMMC:
                String domainNickName = userInfo.getDomainNickName();
                // 校验该用户是否已经完成任务
                Object rewardData = redisUtils.get(key);
                // 查看数据库是否有该 任务完成记录
                if (rewardData == null) {
                    TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 不为空说明已经完成任务 存入缓存
                    if (taskCenterUser != null) {
                        redisUtils.set(key, taskCenterUser);
                    }
                }
                Object rewardData1 = redisUtils.get(key);
                if (StrUtil.isNotBlank(domainNickName) && rewardData1 == null) {
                    // 完成任务 赠送奖励
                    TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                    taskCenterUserNew.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setTaskIdCompleted(userInfo.getUuid()).setReward(taskCenter.getReward())
                        .setIsComplete(1).setCompletionTime(new Date()).setCompletionCount(1);
                    // taskCenterUserMapper.insert(taskCenterUserNew);
                    insertTaskCenterUser(taskCenterUserNew, userInfo);
                }

                break;
            // 首次设置NFT头像
            case SZNFTTX:
                Long headPortraitNftId = userInfo.getHeadPortraitNftId();
                // 校验该用户是否已经完成任务
                Object rewardNFTData = redisUtils.get(key);
                // 查看数据库是否有该 任务完成记录
                if (rewardNFTData == null) {
                    TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 不为空说明已经完成任务 存入缓存
                    if (taskCenterUser != null) {
                        redisUtils.set(key, taskCenterUser);
                    }
                }
                Object rewardNFTData1 = redisUtils.get(key);
                if (headPortraitNftId != null && rewardNFTData1 == null) {
                    // 完成任务 赠送奖励
                    TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                    taskCenterUserNew.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setTaskIdCompleted(userInfo.getUuid()).setReward(taskCenter.getReward())
                        .setIsComplete(1).setCompletionTime(new Date()).setCompletionCount(1);
                    // taskCenterUserMapper.insert(taskCenterUserNew);
                    insertTaskCenterUser(taskCenterUserNew, userInfo);
                }
                break;
            // 首次关注好友
            case SCGZHY:
                // 校验该用户是否已经完成任务
                Object rewardFollowData = redisUtils.get(key);

                // 查看数据库是否有该 任务完成记录
                if (rewardFollowData == null) {
                    TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 不为空说明已经完成任务 存入缓存
                    if (taskCenterUser != null) {
                        redisUtils.set(key, taskCenterUser);
                    }
                }

                Object rewardFollowData1 = redisUtils.get(key);
                // 判断是否完成任务
                List<Follow> follows = followMapper
                    .selectList(Wrappers.<Follow>lambdaQuery().eq(Follow::getFollowUuid, userInfo.getUuid()));

                if (CollectionUtil.isNotEmpty(follows) && rewardFollowData1 == null) {
                    // 完成任务 赠送奖励
                    TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                    taskCenterUserNew.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setTaskIdCompleted(userInfo.getUuid()).setReward(taskCenter.getReward())
                        .setIsComplete(1).setCompletionTime(new Date()).setCompletionCount(1);
                    // taskCenterUserMapper.insert(taskCenterUserNew);
                    insertTaskCenterUser(taskCenterUserNew, userInfo);
                }

                break;
            // 首次获得粉丝
            case SCHDFS:
                // 校验该用户是否已经完成任务
                Object rewardFSData = redisUtils.get(key);
                // 查看数据库是否有该 任务完成记录
                if (rewardFSData == null) {
                    TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 不为空说明已经完成任务 存入缓存
                    if (taskCenterUser != null) {
                        redisUtils.set(key, taskCenterUser);
                    }
                }
                Object rewardFSData1 = redisUtils.get(key);
                // 判断是否完成任务
                List<Follow> follows1 = followMapper.selectList(
                    Wrappers.<Follow>lambdaQuery().eq(Follow::getAccountUuid, userInfo.getUuid()));
                if (CollectionUtil.isNotEmpty(follows1) && rewardFSData1 == null) {
                    // 完成任务 赠送奖励
                    TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                    taskCenterUserNew.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setTaskIdCompleted(userInfo.getUuid()).setReward(taskCenter.getReward())
                        .setIsComplete(1).setCompletionTime(new Date()).setCompletionCount(1);
                    // taskCenterUserMapper.insert(taskCenterUserNew);
                    insertTaskCenterUser(taskCenterUserNew, userInfo);
                }
                break;
            // 首次获得点赞
            case SCHDDZ:
                // 校验该用户是否已经完成任务
                Object rewardDZData = redisUtils.get(key);
                // 查看数据库是否有该 任务完成记录
                if (rewardDZData == null) {
                    TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 不为空说明已经完成任务 存入缓存
                    if (taskCenterUser != null) {
                        redisUtils.set(key, taskCenterUser);
                    }
                }
                Object rewardDZData1 = redisUtils.get(key);
                // 判断是否完成任务
                List<Trends> trends = trendsMapper
                    .selectList(Wrappers.<Trends>lambdaQuery().eq(Trends::getAccountUuid, userInfo.getUuid())
                        .eq(Trends::getRemoveFlag, 0).ge(Trends::getLikesNum, 1));

                if (CollectionUtil.isNotEmpty(trends) && rewardDZData1 == null) {
                    // 完成任务 赠送奖励
                    TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                    taskCenterUserNew.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setTaskIdCompleted(userInfo.getUuid()).setReward(taskCenter.getReward())
                        .setIsComplete(1).setCompletionTime(new Date()).setCompletionCount(1);
                    // taskCenterUserMapper.insert(taskCenterUserNew);
                    insertTaskCenterUser(taskCenterUserNew, userInfo);
                }
                break;

            // 首次发布视频动态
            case SCFBSPDD:
                // 校验该用户是否已经完成任务
                Object rewardSPData = redisUtils.get(key);
                // 查看数据库是否有该 任务完成记录
                if (rewardSPData == null) {
                    TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                        .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                        .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                    // 不为空说明已经完成任务 存入缓存
                    if (taskCenterUser != null) {
                        redisUtils.set(key, taskCenterUser);
                    }
                }
                Object rewardSPData1 = redisUtils.get(key);
                // 判断是否完成任务
                List<Trends> trendsSP = trendsMapper
                    .selectList(Wrappers.<Trends>lambdaQuery().eq(Trends::getAccountUuid, userInfo.getUuid())
                        .eq(Trends::getType, 5).eq(Trends::getRemoveFlag, 0));
                if (CollectionUtil.isNotEmpty(trendsSP) && rewardSPData1 == null) {
                    // 完成任务 赠送奖励
                    TaskCenterUser taskCenterUserNew = new TaskCenterUser();
                    taskCenterUserNew.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setTaskIdCompleted(userInfo.getUuid()).setReward(taskCenter.getReward())
                        .setIsComplete(1).setCompletionTime(new Date()).setCompletionCount(1);
                    // taskCenterUserMapper.insert(taskCenterUserNew);
                    insertTaskCenterUser(taskCenterUserNew, userInfo);
                }
                break;
        }

    }

    /**
     * 检测用户是否发布动态和同步获取点赞数更新到用户任务完成表状态
     *
     * <AUTHOR>
     * @date 2024/05/29
     */
    public void dynamic50AndLike(TaskCenter taskCenter, Account userInfo) {
        // 任务需要完成数量
        Integer completionCount = taskCenter.getCompletionCount();
        // 任务已结束不进行统计=>任务结束时间
        Date completionTime = taskCenter.getCompletionTime();
        // 获取当前时间
        Date date = new Date();
        // 任务开始时间
        Date openingTime = taskCenter.getOpeningTime();
        // 当前时间要小于任务结束时间并且大于任务开始时间
        if (DateUtil.compare(date, completionTime) < 0 && DateUtil.compare(date, openingTime) > 0) {
            // 检查用户是否发表动态且动态是在任务开启之后发布的
            Trends trends = trendsMapper.selectOne(Wrappers.<Trends>lambdaQuery()
                .eq(Trends::getAccountUuid, userInfo.getUuid()).eq(Trends::getRemoveFlag, 0)
                .ge(Trends::getCreateTime, openingTime).orderByDesc(Trends::getLikesNum).last("limit 1"));
            if (trends != null) {
                // 判断是否有任务记录==任务周期内一个任务一个人只能完成一次
                TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(Wrappers
                    .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                // 判断是否完成任务
                // 实际点赞数量
                Integer likesNum = trends.getLikesNum();
                int isComplete = likesNum >= completionCount ? 1 : 0;
                // 如果没有记录则新增记录
                if (taskCenterUser == null) {
                    TaskCenterUser taskCenterUser1 = new TaskCenterUser();
                    taskCenterUser1.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                        .setTaskIdCompleted(String.valueOf(trends.getId())).setReward(taskCenter.getReward())
                        .setIsComplete(isComplete).setCompletionCount(likesNum);
                    if (isComplete == 1) {
                        taskCenterUser1.setCompletionTime(new Date());
                    }
                    taskCenterUserMapper.insert(taskCenterUser1);
                    // 如果有记录则修改记录
                } else {
                    Integer isComplete1 = taskCenterUser.getIsComplete();
                    Integer completionCount1 = taskCenterUser.getCompletionCount();
                    Integer taskCenterUserId = taskCenterUser.getTaskCenterId();
                    // 未完成任务 且任务id相同 则修改
                    if (taskCenterUserId.equals(taskCenter.getId())) {
                        if (isComplete == 1) {
                            taskCenterUser.setCompletionTime(new Date());
                        }
                        taskCenterUser.setIsComplete(isComplete);
                        taskCenterUser.setCompletionCount(likesNum);
                        taskCenterUser.setTaskIdCompleted(String.valueOf(trends.getId()));
                        taskCenterUserMapper.updateById(taskCenterUser);
                    }
                }
            }
        }
    }

    /**
     * 长期任务=>目前默认是邀请用户任务
     *
     * @param taskCenter
     * @param userInfo
     * <AUTHOR>
     * @date 2024/05/30
     */
    public void inviteUsers(TaskCenter taskCenter, Account userInfo) {
        // 获取当前时间
        Date date = new Date();
        // 任务开始时间
        Date openingTime = taskCenter.getOpeningTime();
        // 当前时间大于任务开始时间
        if (DateUtil.compare(date, openingTime) > 0) {
            // 获取自己邀请的用户且注册时间大于任务开启时间
            List<Account> accounts = accountMapper.selectList(Wrappers.<Account>lambdaQuery()
                .eq(Account::getParentUuid, userInfo.getUuid()).ge(Account::getRegisterTime, openingTime));
            if (CollectionUtil.isNotEmpty(accounts)) {
                // 查询任务表是否有已经重复的任务
                List<TaskCenterUser> taskCenterUsers = taskCenterUserMapper.selectList(Wrappers
                    .<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId()));
                // 比较两个集合的 差集 更新没有的任务
                if (CollectionUtil.isNotEmpty(taskCenterUsers)) {
                    Set<String> taskUUID = taskCenterUsers.stream().map(TaskCenterUser::getTaskIdCompleted)
                        .collect(Collectors.toSet());
                    // 比较出不存在taskCenterUsers中的Account
                    List<Account> NAccounts =
                        accounts.stream().filter(account -> !taskUUID.contains(account.getUuid()))
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(NAccounts)) {
                        List<TaskCenterUser> taskCenterUserList = new ArrayList<>();
                        for (Account account : NAccounts) {
                            TaskCenterUser taskCenterUser = new TaskCenterUser();
                            taskCenterUser.setAccountUuid(userInfo.getUuid())
                                .setTaskCenterId(taskCenter.getId()).setTaskIdCompleted(account.getUuid())
                                .setReward(taskCenter.getReward()).setIsComplete(0).setCompletionCount(0);
                            taskCenterUserList.add(taskCenterUser);
                        }
                        taskCenterUserService.saveBatch(taskCenterUserList);
                    }
                    // 任务表没有 任务 则插入
                } else {
                    List<TaskCenterUser> taskCenterUserList = new ArrayList<>();
                    for (Account account : accounts) {
                        TaskCenterUser taskCenterUser = new TaskCenterUser();
                        taskCenterUser.setAccountUuid(userInfo.getUuid()).setTaskCenterId(taskCenter.getId())
                            .setTaskIdCompleted(account.getUuid()).setReward(taskCenter.getReward())
                            .setIsComplete(0).setCompletionCount(0);
                        taskCenterUserList.add(taskCenterUser);
                    }
                    taskCenterUserService.saveBatch(taskCenterUserList);
                }
            }
            // 查询出任务表未完成任务的
            List<TaskCenterUser> taskCenterUsers = taskCenterUserMapper.selectList(
                Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId())
                    .eq(TaskCenterUser::getIsComplete, 0));
            // 发布一条动态 才算完成任务
            if (CollectionUtil.isNotEmpty(taskCenterUsers)) {
                List<String> taskUUID = taskCenterUsers.stream().map(TaskCenterUser::getTaskIdCompleted)
                    .collect(Collectors.toList());
                // 查询这些人是否发布了动态 且发布动态的时间 大于任务开启时间
                List<Trends> trends = trendsMapper
                    .selectList(Wrappers.<Trends>lambdaQuery().in(Trends::getAccountUuid, taskUUID)
                        .eq(Trends::getRemoveFlag, 0).ge(Trends::getCreateTime, openingTime));

                if (CollectionUtil.isNotEmpty(trends)) {
                    List<String> accountUUID =
                        trends.stream().map(Trends::getAccountUuid).collect(Collectors.toList());
                    // 更新任务列表任务状态
                    taskCenterUserMapper.update(null,
                        Wrappers.<TaskCenterUser>lambdaUpdate()
                            .eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                            .eq(TaskCenterUser::getTaskCenterId, taskCenter.getId())
                            .in(TaskCenterUser::getTaskIdCompleted, accountUUID)
                            .set(TaskCenterUser::getIsComplete, 1).set(TaskCenterUser::getCompletionCount, 1)
                            .set(TaskCenterUser::getCompletionTime, date));
                }
            }
        }
    }

    public void insertTaskCenterUser(TaskCenterUser taskCenterUserNew, Account userInfo) {
        ReentrantLock userLock = userLocks.computeIfAbsent(userInfo.getUuid(), k -> new ReentrantLock());
        userLock.lock();
        try {
            TaskCenterUser taskCenterUser = taskCenterUserMapper.selectOne(
                Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getAccountUuid, userInfo.getUuid())
                    .eq(TaskCenterUser::getTaskCenterId, taskCenterUserNew.getTaskCenterId()));
            if (taskCenterUser == null) {
                taskCenterUserMapper.insert(taskCenterUserNew);
            }
        } finally {
            userLock.unlock();
        }
    }

    private Boolean checkRefreshCount(Account userInfo) {
        int seconds = 5;
        int maxCount = 1;
        String key = "statisticalTasks:" + userInfo.getUuid();
        Optional<Object> optionalValue = Optional.ofNullable(redisUtils.get(key));
        int count =
            optionalValue.filter(value -> value instanceof Integer).map(value -> (Integer)value).orElse(0); // 如果 key 不存在或值不是 Integer，则默认值为 0
        log.debug("检测已经访问的次数{}", count);
        if (count <= 0) {
            redisUtils.set(key, 1, seconds);
            return true;
        }
        if (count < maxCount) {
            redisUtils.increment(key);
            return true;
        }
        log.warn("请求过于频繁请稍后再试");
        return false;
    }
}

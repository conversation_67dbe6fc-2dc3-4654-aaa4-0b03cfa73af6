package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.domain.resp.InviteCodeAccountResp;
import com.lj.auth.domain.vo.AccountVo;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.List;

public interface AccountService extends IService<Account> {

    /**
     * 请求短信验证码
     *
     * @param account 手机号码
     * @param state 1:注册 2:登录 3:不判断场景
     * @return {@link R}
     */
    R sentCaptcha(String account, Integer state);

    /**
     * 内部使用获取用户信息
     *
     * @return {@link Account }
     * <AUTHOR>
     * @date 2024/03/27
     */
    Account getUserInfo();

    /**
     * 获取详细用户信息
     * 
     * @return {@link AccountVo }
     * <AUTHOR>
     * @date 2024/04/08
     */
    AccountVo getAllUserInfo();

    /**
     * 内部使用获取用户信息
     *
     * @param uuid
     * @return {@link AccountVo }
     */
    AccountVo getAllUserInfo(String uuid);

    /**
     * 验证码登录
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    R loginByCode(HttpServletRequest request, String account, String varificationCode);

    /**
     * 对比缓存中的验证码
     *
     * @param account 手机号
     * @param verificationCode 验证码
     * @return boolean
     * <AUTHOR>
     * @date 2024/03/27
     */
    boolean verifyVerificationCode(String account, String verificationCode);

    /**
     * 设置图像
     *
     * @param portrait 图像
     * @param nftId nftId
     * @param type 图像类型 1-普通图像 2-nft图像
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/17
     */
    R setPortrait(String portrait, Long nftId, Integer type);

    /**
     * 同步im信息
     * @param
     */
     void syncImInfo(Account userInfo);

    R setPayPassword(String account, String varificationCode, String payPassword);

    /**
     * 修改密码
     * 
     * @param account 手机号
     * @param varificationCode 验证码
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R setPassword(String account, String varificationCode, String password);

    /**
     * 忘记密码
     * 
     * @param account 手机号
     * @param varificationCode 验证码
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R forgetPassword(String account, String varificationCode, String password);

    /**
     * 设置昵称
     * 
     * @param nickName 昵称
     * @param domainNickName 域名昵称
     * @param type 1-昵称展示2-域名昵称展示
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R setNickName(String nickName, String domainNickName, Integer type);

    /**
     * 接口操作记录
     * 
     * @param account 基础账号
     * @param realIp 最近登录IP
     * @param state 操作状态 0-失败 1-成功
     * @param requestParam 请求参数
     * @param requestInterface 请求接口
     * <AUTHOR>
     * @date 2024/03/28
     */
    void addAccountLoginInfo(String account, String realIp, int state, String requestParam,
        String requestInterface);

    /**
     * 注册
     * 
     * @param request http请求
     * @param account 账户
     * @param varificationCode 验证码
     * @param password 密码
     * @param parentUUID 邀请人用户uuid
     * @param inviteCode 邀请码
     * @return
     */
    R register(HttpServletRequest request, String account, String varificationCode, String password,
        String parentUUID, String inviteCode);

    /**
     * 密码登录
     * 
     * @param request 请求
     * @param account 手机号
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/08
     */
    R loginByPassword(HttpServletRequest request, String account, String password);

    /**
     * 仅限运营账号登录
     * 
     * @param request
     * @param account
     * @param password
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/17
     */
    R loginByPasswordV1(HttpServletRequest request, String account, String password);

    /**
     * 运营账号和正常账号都能登录
     *
     * @param request
     * @param account
     * @param password
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/17
     */
    R loginByPasswordV2(HttpServletRequest request, String account, String password);

    /**
     * 通过手机号获取用户信息
     * 
     * @param phone 手机号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/09
     */
    R getPhoneAllUserInfo(String phone);

    /**
     * 注册协议
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    R getUserAgreementService();

    /**
     * 我的推广
     *
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    R myPromotionService(int page, int pageSize);


    /**
     * 福穗签约
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/11
     */
    R uploadPeopleService();

    /**
     * 福穗签约
     *
     * @param idCard -身份证号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/23
     */
    R uploadPeopleService(String name, String idCard);

    /**
     * 游客模式
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/12
     */
    R touristService(String deviceId,String appId,Integer type);

    /**
     * 账号注销协议
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/14
     */
    R cancellationAgreementService();

    /**
     * 注销账号
     * 
     * @param ids
     * @param reason
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/14
     */
    R accountCancellationService(List<Integer> ids, String reason);

    /**
     * 判断手机号是否存在
     * 
     * @param account-手机号
     * @return boolean
     * <AUTHOR>
     * @date 2024/06/14
     */
     boolean handleRegistrationScenario(String account);

    /**
     * 注销账号检测
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/19
     */
    R accountDetectionService();

    /**
     * 注销提醒操作
     * 
     * @param id-条件id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/19
     */
    R conditionalOperationService(Integer id);

    /**
     * 修改手机号
     *
     * @param account
     * @param varificationCode
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/20
     */
    R setPhoneNumberService(String account, String varificationCode);

    /**
     * 修改手机号 (内部调用)
     *
     * @param uuid
     * @param account
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/20
     */
    R setPhoneNumberService1(String uuid, String account);

    /**
     * 修改手机号 (内部调用)
     * 手机号账户绑定DID账户
     *
     * @param phone
     * @param didsymbol
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/20
     */
    R setPhoneNumberService2(String phone, String didsymbol);

    /**
     * 获取渠道信息
     * @param inviteCode
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/12
     */
    R getOperateInfoService(String inviteCode);


    /**
     * 同步用户登录信息
     * @param ip
     * @return {@link Object }
     * <AUTHOR>
     * @date 2025/04/09
     */
    Object logInMessageRecordService(@NotBlank(message = "ip地址不能为空") String ip,String application);
    
    
    /**
     * 邀请码查询用户信息
     * @param inviteCode
     * @return
     */
    InviteCodeAccountResp getByInviteCode(@NotBlank(message = "邀请码不能为空") String inviteCode);

    /**
     * 福穗用户签约
     * 
     * @param name
     * @param idCard
     * @param phone
     * @return {@link R }
     */
    R uploadPeopleIdCardV2Service(String name, String idCard, String phone);
}

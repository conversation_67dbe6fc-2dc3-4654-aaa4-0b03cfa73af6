package com.lj.auth.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.Result.SpBalanceReq;
import com.lj.auth.domain.Result.SpTransferReq;
import com.lj.auth.domain.Result.TransferReq;
import com.lj.auth.domain.Result.TransferRequireInfo;
import com.lj.auth.entity.SpTransactionRecords;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.SpAddressBlacklistMapper;
import com.lj.auth.mapper.SpTransactionRecordsMapper;
import com.lj.auth.util.BNBUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.ChainMapper;
import com.lj.auth.domain.Chain;
import com.lj.auth.service.ChainService;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import javax.annotation.Resource;

@Slf4j
@Service
public class ChainServiceImpl extends ServiceImpl<ChainMapper, Chain> implements ChainService {
    @Value("${ylChainId}")
    private Integer chainId;

    @Resource
    private SpAddressBlacklistMapper spAddressBlacklistMapper;
    private SpAddressBlacklistMapper spAddressBlacklistMapper;

    @Override
    public Chain getLjChainInfoService() {
        Chain chain = this.getOne(Wrappers.<Chain>lambdaQuery().eq(Chain::getChainId, chainId));
        return chain;
    }

    /**
     * 获取所有链信息
     * @return {@link List }<{@link Chain }>
     * <AUTHOR>
     * @date 2024/04/16
     */
    @Override
    public List<Chain> getChainInfoService() {
        List<Chain> list = this.list(Wrappers.<Chain>lambdaQuery().eq(Chain::getState, true));
        return list;
    }


    /**
     * 获取sp余额信息
     * @param jsonObject
     * @return
     */
    @Override
    public SpBalanceReq getBalance(JSONObject jsonObject) {
        String address = jsonObject.getString("address");
        if (address == null) {
            throw new RuntimeException("address is null");
        }
        SpBalanceReq spBalanceReq=new SpBalanceReq();
        spBalanceReq.setAddress(address);
        BigDecimal balance = getBalance(address);
        spBalanceReq.setBalance(balance);
        return spBalanceReq;
    }

    private BigDecimal getBalance(String address) {
        BigInteger balance = BNBUtil.getBalance(address);
        BigDecimal ylAsset = new BigDecimal(balance).divide(BigDecimal.TEN.pow(18));
        //向下取整6为位小数
        ylAsset = ylAsset.setScale(6, BigDecimal.ROUND_DOWN);
        return ylAsset;
    }


    /**
     * sp转账
     * @param jsonObject
     * @return
     */
    @Override
    public SpTransferReq transferSP(JSONObject jsonObject) {
        SpTransferReq spTransferReq=new SpTransferReq();
        String signedData = jsonObject.getString("signedData");
        String fromAddress = jsonObject.getString("fromAddress");
        String toAddress = jsonObject.getString("toAddress");
        BigDecimal amount = jsonObject.getBigDecimal("amount");
        if (signedData==null|| fromAddress == null || toAddress == null || amount == null) {
            throw new RuntimeException("fromAddress or toAddress or amount is null");
        }
        //校验地址是否黑名单
        validateAressddBlackList(fromAddress);
        BigInteger GAS_LIMIT = new BigInteger("3000000");
        String transactionHash = BNBUtil.sendTransaction(signedData, fromAddress, toAddress, amount,GAS_LIMIT);
        spTransferReq.setTransactionHash(transactionHash);
        TransferReq transferReq = queryTxInfo(transactionHash);
        spTransferReq.setIsStatusOK(transferReq.getIsStatusOK());
        return spTransferReq;
    }

    @Override
    public String getSign(JSONObject jsonObject) {
        String privateKey = jsonObject.getString("privateKey");
        String toAddress = jsonObject.getString("toAddress");
        String amount = jsonObject.getString("amount");
        String address = BNBUtil.getAddressByPrivateKey(privateKey);
        log.info("fromAddress:{}",address);
        TransferRequireInfo transferRequireInfo = queryTxRequireInfo(address);
        BigInteger nonce = transferRequireInfo.getNonce();
        BigInteger gasPrice = transferRequireInfo.getGasPrice();
        BigInteger gasLimit = transferRequireInfo.getGasLimit();
        BigInteger value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger();
        //16进制
        String transSpData="转让sp";
        //transSpData 转成16进制
        try {
            transSpData = Numeric.toHexString(transSpData.getBytes("UTF-8"));
        }        catch (Exception e) {
            e.printStackTrace();
        }
        String signedData="";
        try {
            signedData = BNBUtil.signTransaction(nonce, gasPrice, gasLimit, toAddress.toLowerCase(), value, transSpData,
                    chainId, privateKey);
            System.out.println("signedData:" + signedData);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return signedData;
    }


    /**
     * 获取交易所需信息
     * @param jsonObject
     * @return
     */
    @Override
    public TransferRequireInfo queryTxRequireInfo(JSONObject jsonObject) {
        String fromAddress = jsonObject.getString("fromAddress");
        if (fromAddress == null) {
            throw new RuntimeException("address is null");
        }
        //校验地址是否黑名单
        validateAressddBlackList(fromAddress);
        TransferRequireInfo transferRequireInfo = queryTxRequireInfo(fromAddress);
        return transferRequireInfo;
    }


    private void validateAressddBlackList(String fromAddress){
        Boolean blacklist = spAddressBlacklistMapper.isBlacklist(fromAddress);
        if (blacklist) {
            throw new ServiceException("检测到该地址存在异常操作，为确保信息安全，系统已锁定该地址");
        }
    }


    private TransferRequireInfo queryTxRequireInfo(String fromAddress) {
        //校验地址是否黑名单
        validateAressddBlackList(fromAddress);
        TransferRequireInfo transferRequireInfo=new TransferRequireInfo();
        transferRequireInfo.setFromAddress(fromAddress);
        //获取余额
        BigDecimal balance = getBalance(fromAddress);
        transferRequireInfo.setBalance(balance);
        //获取nonce
        BigInteger nonce = BNBUtil.getTransactionNonce(fromAddress);
        transferRequireInfo.setNonce(nonce);

        //获取gasLimit
        BigInteger GAS_LIMIT = new BigInteger("3000000");
        transferRequireInfo.setGasLimit(GAS_LIMIT);
        try {
            BigInteger gasPrice = BNBUtil.ethGasPrice().getGasPrice();
            transferRequireInfo.setGasPrice(gasPrice);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return transferRequireInfo;
    }


    /**
     * 查询交易信息
     * @param jsonObject
     * @return
     */
    @Override
    public TransferReq queryTxInfo(JSONObject jsonObject) {
            return null;
    }


    /**
     * 通过交易hash查询 交易信息
     * @param transactionHash
     * @return
     */
    @Override
    public TransferReq queryTxInfo(String transactionHash) {
        TransferReq transferReq=new TransferReq();
        transferReq.setTransactionHash(transactionHash);
        try {
            TransactionReceipt transactionReceipt = getTransactionReceipt(transactionHash);
            if (!transactionReceipt.isStatusOK()) {
                transferReq.setIsStatusOK(false);
            }else {
                transferReq.setIsStatusOK(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("查询交易信息遇到网络错误");
        }
        return transferReq;
    }





    public TransactionReceipt getTransactionReceipt(String transactionHash) {
        try {
            EthGetTransactionReceipt ethGetTransactionReceipt =
                    BNBUtil.ethGetTransactionReceipt(transactionHash);
            Optional<TransactionReceipt> optional = ethGetTransactionReceipt.getTransactionReceipt();
            int count = 0;
            if (!optional.isPresent()) {
                while (count < 20) {
                    count++;
                    // 等待1秒
                    log.info("等待" + count + "秒");
                    TimeUnit.SECONDS.sleep(1);
                    ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
                    optional = ethGetTransactionReceipt.getTransactionReceipt();
                    if (optional.isPresent()) {
                        return optional.get();
                    }
                }
                throw new RuntimeException(transactionHash + " 你太慢了");
            }
            return optional.get();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取交易回执失败");
        }
    }


    private void addTransactionInfo(TransactionReceipt transactionReceipt) {
        SpTransactionRecords spTransactionRecords = new SpTransactionRecords();

        try {
            String transactionHash = transactionReceipt.getTransactionHash();
            // 交易哈希
            spTransactionRecords.setTxHash(transactionHash);

            // 区块号
            spTransactionRecords.setBlockNumber(transactionReceipt.getBlockNumber().longValue());

            // 交易状态（status 为 "0x1" 表示成功，"0x0" 失败）
            spTransactionRecords.setStatus(
                    "0x1".equals(transactionReceipt.getStatus()) ? 1 : 2
            );

            // Gas 费用 = gasUsed * effectiveGasPrice
            BigInteger gasUsed = transactionReceipt.getGasUsed();
            BigInteger effectiveGasPrice = transactionReceipt.getEffectiveGasPrice();
            if (gasUsed != null && effectiveGasPrice != null) {
                spTransactionRecords.setGasFee(gasUsed.multiply(effectiveGasPrice));
            }

            // ============ 关键点 ============
            // TransactionReceipt 没有 from、to、value，需要再查一次原始交易
            EthTransaction ethTransaction = null;
            try {
                ethTransaction = BNBUtil.ethGetTransactionByHash(transactionHash);
            } catch (IOException e) {
                e.printStackTrace();
                throw new ServiceException("交易不存在");
            }
            Optional<Transaction> transaction = ethTransaction.getTransaction();
            if (!transaction.isPresent()) {
                throw new ServiceException("交易不存在");
            }
            Transaction transactionInfo = transaction.get();
            BigInteger value = transactionInfo.getValue();
            BigDecimal fixUsed = new BigDecimal(value).divide(BigDecimal.TEN.pow(18));
            BigInteger blockNumber = transactionInfo.getBlockNumber();
            EthBlock ethBlock = null;
            try {
                ethBlock = BNBUtil.ethGetBlockByNumber(blockNumber, false);
            } catch (IOException e) {
                e.printStackTrace();
                throw new ServiceException("区块不存在");
            }
            EthBlock.Block block = ethBlock.getBlock();
            BigInteger timestamp = block.getTimestamp();
            String strTime = DateUtil.date(timestamp.multiply(new BigInteger("1000")).longValue())
                    .toString("yyyy-MM-dd HH:mm:ss");
            spTransactionRecords.setTransactionTime(strTime);
                spTransactionRecords.setFromAddress(transactionInfo.getFrom());
                spTransactionRecords.setToAddress(transactionInfo.getTo());
                spTransactionRecords.setAmount(transactionInfo.getValue().divide(BigDecimal.TEN.pow(18)); // 主币数量
                spTransactionRecords.setChainId(transactionInfo.getChainId().orElse(BigInteger.ZERO));

                // 主币交易 vs 代币交易判断
                if (transactionInfo.getTo() != null && transactionInfo.getInput() != null && transactionInfo.getInput().length() > 2) {
                    // 简单判断是否代币转账：input 非空
                    spTransactionRecords.setTxType(2);
                } else {
                    spTransactionRecords.setTxType(1);
                }

            // 符号和合约地址（需要你自己在业务层判断填充，比如 ETH 主币、ERC20 代币）
            spTransactionRecords.setSymbol("SP");
            spTransactionRecords.setContractAddress(null);

            // 时间
            Date nowDate = new Date();
            spTransactionRecords.setCreateTime(nowDate);
            spTransactionRecords.setUpdateTime(nowDate);

        } catch (Exception e) {
            e.printStackTrace();
        }

        // TODO: 保存到数据库，比如 transactionRecordsMapper.insert(spTransactionRecords)
    }

}

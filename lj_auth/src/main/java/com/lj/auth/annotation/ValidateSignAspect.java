package com.lj.auth.annotation;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.lj.auth.common.R;
import com.lj.auth.util.HmacSHA256Util;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;


import lombok.extern.slf4j.Slf4j;

/**
 * @Author: wxm
 * @Description:
 * @Date: 2023/7/26 16:34
 */
@Aspect
@Component
@Slf4j
@EnableAspectJAutoProxy
public class ValidateSignAspect {

    @Pointcut("@annotation(com.lj.auth.annotation.ValidateSign)")
    public void pointCut() {
    }

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Value("${paramSecretKey}")
    private String paramSecretKey;

    @Value("${paramSignFlag}")
    private boolean paramSignFlag;


    @Around("pointCut()")
    public Object validateToken(ProceedingJoinPoint joinPoint) throws Throwable {
        //获取参数
        Object[] args = joinPoint.getArgs();
        if (!paramSignFlag){
            return joinPoint.proceed(args);
        }
        HttpServletRequest request = null;
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof HttpServletRequest) {
                request = (HttpServletRequest) args[i];
                break;
            }
        }
        if (request == null) {
            return R.error("参数为空");
        }

        //如果是get请求，直接放行
        if (request.getMethod().equalsIgnoreCase("GET")) {
            return joinPoint.proceed(args);
        }

        //从请求头中获取时间戳和nonce值
        String timestamp = request.getHeader("timestamp");
        String nonce = request.getHeader("nonce");
        String headerSign = request.getHeader("sign");

        if(StringUtils.isAnyEmpty(timestamp, nonce, headerSign)){
            return R.error("缺少签名参数");
        }

        // 验证时间戳 300秒内有效
        if (System.currentTimeMillis() - Long.parseLong(timestamp) > 300000) {
            return R.error("请求已过期");
        }
        //请求接口，eg：/lj-square/trends/likes
        String requestURI = request.getRequestURI();

        // 验证nonce唯一性
        if (stringRedisTemplate.opsForValue().setIfAbsent("nonce:" + nonce+"_"+requestURI, "1", 5, TimeUnit.MINUTES) == null) {
            return R.error("重复请求");
        }
        //如果是表单提交(或文件上传)
        if (request.getContentType().contains("multipart/form-data")) {
            Map<String, String[]> parameterMap = request.getParameterMap();
            Set<String> keySet = parameterMap.keySet();
            if (keySet.size() == 0) {
                return joinPoint.proceed(args);
            } else {
                Map<String, String> params = new HashMap<>();
                for (String key : keySet) {
                    params.put(key, parameterMap.get(key)[0]);
                }
                String signContent = HmacSHA256Util.buildSignContent(params);
                String sign = HmacSHA256Util.sign(signContent, paramSecretKey);
                if (!sign.equals(headerSign)) {
                    return R.error("签名不合法");
                }
            }
        } else if (request.getContentType().contains("application/json")) {
            //为空，直接返回
            String json = IOUtils.toString(request.getInputStream(), "utf-8");
            if (StringUtils.isBlank(json)) {
                return joinPoint.proceed(args);
            }
            // 验证签名
            try {
                Map<String, String> params = new HashMap<>();
                JSONObject jsonObject = JSONObject.parseObject(json);
                for (String key : jsonObject.keySet()) {
                    if ("sign".equals(key) || "nonce".equals(key) || "timestamp".equals(key)) {
                        continue;
                    }
                    Object value = jsonObject.get(key);
                    if (value != null && !"".equals(value.toString())) {
                        params.put(key, value.toString());
                    }
                }
                String signContent = HmacSHA256Util.buildSignContent(params);
                String sign = HmacSHA256Util.sign(signContent, paramSecretKey);
                if (!sign.equals(headerSign)) {
                    return R.error("签名不合法");
                }
                return joinPoint.proceed(args);
            } catch (Exception e) {
                e.printStackTrace();
                return R.error(500, e.getMessage());
            }
        }
        return joinPoint.proceed(args);
    }

}

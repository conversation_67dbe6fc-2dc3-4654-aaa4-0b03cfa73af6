package com.lj.auth.domain.Result;

import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @describe  发起交易所需信息
 */


@Data
public class TransferRequireInfo {

    /**
     * 发起交易地址
     */
    private String fromAddress;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 交易nonce
     */
    private BigInteger nonce;

    /**
     * gass费
     */
    private BigInteger gasPrice;

    /**
     * 交易限制
     */
    private BigInteger gasLimit;
}

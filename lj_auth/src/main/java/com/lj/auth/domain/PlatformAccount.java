package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 云链平台账户表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_platform_account")
public class PlatformAccount {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 链地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 私钥
     */
    @TableField(value = "private_key")
    private String privateKey;

    /**
     * 公钥
     */
    @TableField(value = "public_key")
    private String publicKey;

    /**
     * 链账户名称
     */
    @TableField(value = "chain_account_name")
    private String chainAccountName;

    /**
     * 用处  1-领NFT 2-平台能量充值3-签到赠送 4-公司收款退款账户 5-域名注册解析管理账户
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 标识  1-不隐藏   0-隐藏
     */
    @TableField(value = "`status`")
    private Boolean status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;




    /**
     * 地址类型- 5：域名注册解析管理账户
     *
     */
    public static final int TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT = 5;


    /**
     * 地址类型- 6 NFT回收地址
     */
    public static final int TYPE_NFT_RECYCLE_ACCOUNT = 6;

    /**
     *
     *
     * 地址状态 不隐藏
     */
    public static final int STATUS_SHOW = 1;
}